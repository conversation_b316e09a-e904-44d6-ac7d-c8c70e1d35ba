<div class="hbox hbox-auto-xs hbox-auto-sm" refresh-on="OrganizationChanged ProjectChanged" refresh-action="vm.get(vm.currentOptions)" refresh-throttle="10000">
  <div class="col" refresh-on=" PlanChanged PersistentEventChanged" refresh-action="vm.get(vm.currentOptions, false)" refresh-throttle="10000">
    <div class="wrapper-md">
      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-group"></i> {{::'My Organizations' | translate}}</div>
        <div class="table-responsive">
          <table class="table table-striped table-bordered table-fixed b-t">
            <thead>
            <tr>
              <th>{{::'Name' | translate}}</th>
              <th>{{::'Plan' | translate}}</th>
              <th class="number hidden-xs"><abbr title="{{::'Total number of projects' | translate}}">{{::'Projects' | translate}}</abbr></th>
              <th class="number hidden-xs"><abbr title="{{::'Total number of stacks' | translate}}">{{::'Stacks' | translate}}</abbr></th>
              <th class="number hidden-xs"><abbr title="{{::'Total number of events within the retention period' | translate}}">{{::'Events' | translate}}</abbr></th>
              <th class="action">{{::'Actions' | translate}}</th>
            </tr>
            </thead>
            <tbody>
              <tr class="row-clickable" ng-repeat="organization in vm.organizations | orderBy: 'name' track by organization.id">
                <td ng-click="vm.open(organization.id, $event)">{{organization.name}}</td>
                <td ng-click="vm.open(organization.id, $event)">{{organization.plan_name}}</td>
                <td ng-click="vm.open(organization.id, $event)" class="number hidden-xs">{{organization.project_count | number:0}}</td>
                <td ng-click="vm.open(organization.id, $event)" class="number hidden-xs">{{organization.stack_count | number:0}}</td>
                <td ng-click="vm.open(organization.id, $event)" class="number hidden-xs">{{organization.event_count | number:0}}</td>
                <td>
                  <div class="btn-group">
                    <button type="button" role="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
                      <i class="fa fa-fw fa-edit"></i> <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right" role="menu">
                      <li><a ui-sref="app.organization.manage({ id: organization.id })"><i class="fa fa-fw fa-edit"></i> {{::'Edit' | translate}}</a></li>
                      <li ng-if="vm.canChangePlan"><a ng-click="vm.changePlan(organization.id)"><i class="fa fa-credit-card fa-fw"></i> {{::'Change Plan' | translate}}</a></li>
                      <li><a ui-sref="app.organization.manage({ id: organization.id, tab: 'billing' })"><i class="fa fa-fw fa-file"></i> {{::'View Invoices' | translate}}</a></li>
                      <li><a ng-click="vm.leave(organization, appVm.user)"><i class="fa fa-fw fa-sign-out"></i> {{::'Leave Organization' | translate}}</a></li>
                      <li><a ng-click="vm.remove(organization)"><i class="fa fa-fw fa-times"></i> {{::'Delete' | translate}}</a></li>
                    </ul>
                  </div>
                </td>
              </tr>
              <tr ng-if="vm.organizations.length === 0 || vm.loading">
                <td colspan="6">
                  <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
                  <strong ng-if="!vm.loading">{{vm.hasFilter ? 'No organizations were found with the current filter.': 'No organizations were found.' | translate}}</strong>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="table-footer" ng-if="vm.previous || vm.next">
            <div class="row">
              <div class="col-sm-8 col-xs-8 text-center" ng-if="vm.pageSummary">
                <small class="text-muted inline m-t-xs">{{vm.pageSummary}}</small>
              </div>
              <div class="col-sm-4 col-xs-4 text-right">
                <ul class="pagination pagination-sm m-t-none m-b-none">
                  <li ng-show="vm.currentOptions.page && vm.currentOptions.page > 2"><a ng-click="vm.get()"><i class="fa fa-fast-backward"></i></a></li>
                  <li ng-class="{'disabled': !vm.previous}"><a ng-disabled="!vm.previous" ng-click="!vm.previous || vm.previousPage()"><i class="fa fa-chevron-left"></i></a></li>
                  <li ng-class="{'disabled': !vm.next}"><a ng-disabled="!vm.next" ng-click="!vm.next || vm.nextPage()"><i class="fa fa-chevron-right"></i></a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <footer class="panel-footer">
          <div class="pull-right">
            <a ui-sref="app.frequent" class="btn btn-default" role="button">{{::'Go To Most Frequent' | translate}}</a>
          </div>
          <button type="button" role="button" ng-click="vm.add()" class="btn btn-primary">{{::'New Organization' | translate}}</button>
        </footer>
      </div>
    </div>
  </div>
</div>
