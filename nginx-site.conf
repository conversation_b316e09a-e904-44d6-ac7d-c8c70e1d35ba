# pushState friendly!
# The setup:
#   * website name is `_`
#   * javascript app is located at `/app`

charset utf-8;

tcp_nopush on;
tcp_nodelay off;
client_header_timeout 10s;
client_body_timeout 10s;
client_max_body_size 128k;
reset_timedout_connection on;

gzip on;
gzip_types
    text/css
    text/javascript
    text/xml
    text/plain
    application/javascript
    application/x-javascript
    application/json
    application/xml
    application/rss+xml
    application/atom+xml
    font/truetype
    font/opentype
    image/svg+xml;

server {
  listen 80;
  server_name localhost;
  root /app;

  set $redirect_to_https 0;
  if ($http_x_forwarded_proto != 'https') {
    set $redirect_to_https 1;
  }
  if ($request_uri = '/health') {
    set $redirect_to_https 0;
  }
  if ($redirect_to_https = 1) {
    # rewrite ^ https://$host$request_uri? permanent;
  }

  location / {
    try_files $uri @rewrites;
  }

  location @rewrites {
    rewrite ^(.+)$ /index.html last;
  }

  location /index.html {
    expires -1;
  }

  location /lang {
    expires -1;
  }
}
