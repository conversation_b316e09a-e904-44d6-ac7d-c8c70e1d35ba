<div>
  <rate-limit></rate-limit>

  <div class="container w-auto-xs">
    <div class="text-center m-t">
      <a href="https://exceptionless.com">
        <img src="/img/exceptionless-350.png" alt="logo" />
      </a>
    </div>
    <div class="hbox hbox-auto-xs hbox-auto-sm">
      <div class="col">
        <div class="wrapper-md">
          <div class="panel panel-default">
            <div class="panel-heading"><strong>{{::'Change password' | translate}}</strong></div>
            <div class="panel-body">
              <form name="passwordForm" role="form" class="form-validation form-horizontal">
                <div class="form-group">
                  <label for="password" class="col-sm-2 control-label">{{::'New Password' | translate}}</label>
                  <div class="col-sm-10">
                    <input id="password" name="password" type="password" class="form-control" ng-model="vm.data.password" ng-minlength="6" ng-maxlength="100" ng-required="true" autofocus />
                    <div class="error" ng-messages="passwordForm.password.$error" ng-if="passwordForm.$submitted || passwordForm.password.$touched">
                      <small ng-message="required">{{::'New Password is required.' | translate}}</small>
                      <small ng-message="minlength">{{::'New Password must be at least 6 characters long.' | translate}}</small>
                      <small ng-message="maxlength">{{::'New Password must be less than 101 characters long.' | translate}}</small>
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label for="confirmPassword" class="col-sm-2 control-label">{{::'Confirm password' | translate}}</label>
                  <div class="col-sm-10">
                    <input id="confirmPassword" name="confirmPassword" type="password" class="form-control" ng-model="vm.data.confirm_password" match="vm.data.password" ng-minlength="6" ng-maxlength="100" ng-required="true" />
                    <div class="error" ng-messages="passwordForm.confirmPassword.$error" ng-if="passwordForm.$submitted || passwordForm.confirmPassword.$touched">
                      <small ng-message="match">{{::'New Password and Confirmation Password fields do not match.' | translate}}</small>
                      <small ng-message="required">{{::'Confirm Password is required.' | translate}}</small>
                      <small ng-message="minlength">{{::'Confirm Password must be at least 6 characters long.' | translate}}</small>
                      <small ng-message="maxlength">{{::'Confirm Password must be less than 101 characters long.' | translate}}</small>
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-10">
                    <input type="submit" role="button" class="btn btn-primary" ng-click="vm.changePassword(passwordForm.$valid)" value="{{::'Change Password' | translate}}" />
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
