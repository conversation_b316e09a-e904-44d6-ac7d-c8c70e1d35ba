{"Change Plan": "Change Plan", "Change Password": "Change Password", "Logout": "Logout", "Exceptions": "Exceptions", "Log Messages": "Log Messages", "Broken Links": "Broken Links", "Feature Usages": "Feature Usages", "All": "All", "Reports": "Reports", "Exception Events": "Events", "Message Events": "Events", "Broken Links Events": "Events", "Feature Events": "Events", "Event Events": "Events", "Most Frequent Exceptions": "Most Frequent", "Most Frequent Messages": "Most Frequent", "Most Frequent Links": "Most Frequent", "Most Frequent Features": "Most Frequent", "Most Frequent": "Most Frequent", "Most Frequent Stacks": "Most Frequent Stacks", "Most Users": "Most Users", "Most Users Stacks": "Most Users Stacks", "New Exception": "New Stacks", "New LogMessage": "New Stacks", "New BrokenLink": "New Stacks", "New FeatureUsage": "New Stacks", "New Event": "New", "Sessions": "Sessions", "Project Manager": "Projects", "Organization Manager": "Organizations", "My Account": "My Account", "Documentation": "Documentation", "Support": "Support", "New Stacks": "New Stacks", "Events Per Hour": "Events Per Hour", "History": "History", "Session Events": "Session Events", "Session": "Session", "Session End": "Session End", "Session Heartbeat": "Session Heartbeat", "Feature": "Feature", "Go To Documentation": "Go To Documentation", "Focus Search Bar": "Focus Search Bar", "Go To My Account": "Go To My Account", "Go To Notifications": "Go To Notifications", "Go To Organizations": "Go To Organizations", "Go To Projects": "Go To Projects", "Go To GitHub project": "Go To GitHub project", "Go to public Discord channel": "Go to public Discord channel", "Chat with Support": "Chat with Support", "Your user account was deleted. Please create a new account.": "Your user account was deleted. Please create a new account.", "Billing is currently disabled.": "<PERSON><PERSON> is currently disabled.", "Page Not Found": "Page Not Found", "We're sorry but the page was not found on this site.": "We're sorry but the page was not found on this site.", "Service Status": "Service Status", "We're sorry but the website is currently undergoing maintenance.": "We're sorry but the website is currently undergoing maintenance.", "You'll be automatically redirected when the maintenance is completed.": "You'll be automatically redirected when the maintenance is completed.", "Please enter a Reference Link": "Please enter a Reference Link", "Reference Link": "Reference Link", "Please enter a valid URL": "Please enter a valid URL", "Reference Link is required.": "Reference Link is required.", "Save Reference Link": "Save Reference Link", "UserRatio": "Users", "First": "First", "Last": "Last", "Stack": "<PERSON><PERSON>", "Options": "Options", "Hide": "<PERSON>de", "Future Occurrences Are Critical": "Future Occurrences Are Critical", "Disable Notifications": "Disable Notifications", "Promote To External": "Promote To External", "Add Reference Link": "Add Reference Link", "Title": "Title", "Project": "Project", "Date Fixed": "Date Fixed", "Description": "Description", "Tags": "Tags", "Stacking Information": "Stacking Information", "ExceptionType": "ExceptionType", "Method": "Method", "Chart Options": "Chart Options", "Show Average Value": "Show Average Value", "Average Value": "Average Value", "Show Value Sum": "Show Value Sum", "Value Sum": "Value Sum", "Recent Occurrences": "Recent Occurrences", "Reference Links": "Reference Links", "Mark Fixed": "<PERSON>", "No stacks were found with the current filter.": "No stacks were found with the current filter.", "No stacks were found.": "No stacks were found.", "Future Stack Occurrences are Not Critical": "Future Stack Occurrences are Not Critical", "Future Stack Occurrences are Critical": "Future Stack Occurrences are Critical", "Enable Stack Notifications": "Enable Stack Notifications", "Disable Stack Notifications": "Disable Stack Notifications", "Promote Stack To External": "Promote Stack To External", "Add Stack Reference Link": "Add Stack Reference Link", "Delete Stack": "Delete Stack", "An error occurred while adding the reference link.": "An error occurred while adding the reference link.", "Successfully promoted stack!": "Successfully promoted stack!", "Manage Integrations": "Manage Integrations", "An error occurred while promoting this stack.": "An error occurred while promoting this stack.", "Are you sure you want to delete this reference link?": "Are you sure you want to delete this reference link?", "An error occurred while deleting the external reference link.": "An error occurred while deleting the external reference link.", "Are you sure you want to delete this stack (includes all stack events)?": "Are you sure you want to delete this stack (includes all stack events)?", "Successfully queued the stack for deletion.": "Successfully queued the stack for deletion.", "An error occurred while deleting this stack.": "An error occurred while deleting this stack.", "The average of all event values": "The average of all event values", "The sum of all event values": "The sum of all event values", "Hide this stack from reports and mutes occurrence notifications": "Hide this stack from reports and mutes occurrence notifications", "All future occurrences will be marked as critical": "All future occurrences will be marked as critical", "Stop sending occurrence notifications for this stack": "Stop sending occurrence notifications for this stack", "Used to promote stacks to external systems.": "Used to promote stacks to external systems.", "Add a reference link to an external resource.": "Add a reference link to an external resource.", "Delete this stack": "Delete this stack", "Summary": "Summary", "Created": "Created", "Duration": "Duration", "Loading...": "Loading...", "Users": "Users", "Average Duration": "Average Duration", "Sessions and Users": "Sessions and Users", "View Active": "View Active", "Recent Sessions": "Recent Sessions", "No sessions were found with the current filter.": "No sessions were found with the current filter.", "No sessions were found.": "No sessions were found.", "Occurred On": "Occurred On", "ended": "ended", "Reference": "Reference", "Level": "Level", "Event Type": "Event Type", "Error Type": "Error Type", "Source": "Source", "Value": "Value", "Message": "Message", "Version": "Version", "Geo": "Geo", "URL": "URL", "User Info": "User Info", "User Email": "User Email", "User Identity": "User Identity", "User Name": "User Name", "User Description": "User Description", "Stack Trace": "Stack Trace", "Machine Name": "Machine Name", "IP Address": "IP Address", "Processor Count": "Processor Count", "Total Memory": "Total Memory", "Available Memory": "Available Memory", "Process Memory": "Process Memory", "OS Name": "OS Name", "OS Version": "OS Version", "Architecture": "Architecture", "Runtime Version": "Runtime Version", "Process ID": "Process ID", "Process Name": "Process Name", "Command Line": "Command Line", "Additional Data": "Additional Data", "Code": "Code", "Submission Method": "Submission Method", "Loaded Modules": "Loaded Modules", "Module Name": "Name", "HTTP Method": "HTTP Method", "Referrer": "<PERSON><PERSON><PERSON>", "Client IP": "Client IP", "User Agent": "User Agent", "Device": "<PERSON><PERSON>", "Browser": "Browser", "Browser OS": "Browser OS", "Post Data": "Post Data", "Event Occurrence": "Event Occurrence", "Go To Stack": "Go To Stack", "Previous Occurrence": "Previous Occurrence", "Next Occurrence": "Next Occurrence", "Toggle View": "Toggle View", "Promote to Tab": "Promote to Tab", "Demote Tab": "<PERSON><PERSON> Tab", "References": "References", "Overview": "Overview", "Exception": "Exception", "Environment": "Environment", "Extended Data": "Extended Data", "Request": "Request", "to enable sessions and other premium features!": "to enable sessions and other premium features!", "is attempting to use a premium feature.": "is attempting to use a premium feature.", "View JSON": "View JSON", "Copy Stack Trace to Clipboard": "<PERSON><PERSON>ack Trace to Clipboard", "Copy Event JSON to Clipboard": "Copy Event JSON to Clipboard", "Copy to Clipboard": "Copy to Clipboard", "Cookie Values": "Cookie Values", "No events were found with the current filter.": "No events were found with the current filter.", "No events were found.": "No events were found.", "An error occurred promoting tab.": "An error occurred promoting tab.", "Cannot_Find_Event": "The event \"{{eventId}}\" could not be found.", "Online": "Online", "Seconds": "{{duration}} seconds", "User": "User", "Bulk Action": "Bulk Action", "Mark Hidden": "<PERSON>", "Sort Descending": "Sort Descending", "Sort Ascending": "Sort Ascending", "Date": "Date", "Please select one or more events": "Please select one or more events", "Successfully queued the events for deletion.": "Successfully queued the events for deletion.", "An error occurred while deleting the events.": "An error occurred while deleting the events.", "Are you sure you want to delete these events?": "Are you sure you want to delete these events?", "DELETE EVENTS": "DELETE EVENTS", "Are you sure you want to delete these stacks (includes all stack events)?": "Are you sure you want to delete these stacks (includes all stack events)?", "DELETE STACKS": "DELETE STACKS", "An error occurred while deleting the stacks.": "An error occurred while deleting the stacks.", "Successfully marked the stacks as fixed.": "Successfully marked the stacks as fixed.", "An error occurred while marking stacks as fixed.": "An error occurred while marking stacks as fixed.", "Successfully queued the stacks to be marked as hidden.": "Successfully queued the stacks to be marked as hidden.", "An error occurred while marking stacks as hidden.": "An error occurred while marking stacks as hidden.", "Please select one or more stacks": "Please select one or more stacks", "Marks the stack as fixed. This will also prevent error occurrences from being displayed in the dashboard.": "Marks the stack as fixed. This will also prevent error occurrences from being displayed in the dashboard.", "Optional:": "Optional:", "Please enter the version in which the stack has been fixed. Any submitted occurrences with a lower version will not cause a regression.": "Please enter the version in which the stack has been fixed. Any submitted occurrences with a lower version will not cause a regression.", "Versioning Documentation": "Versioning Documentation", "Optional Version (Example: 1.2.3)": "Optional Version (Example: 1.2.3)", "Invalid version.": "Invalid version.", "Mark this stack as open": "Mark this stack as open", "Mark this stack as fixed": "Mark this stack as fixed", "Marking Not Fixed": "Marking Not Fixed", "Marking Fixed": "Marking Fixed", "Successfully queued the stacks for deletion.": "Successfully queued the stacks for deletion.", "Stack_Deleted": "The stack \"{{stackId}}\" was deleted.", "Cannot_Find_Stack": "The stack \"{{stackId}}\" could not be found.", "Error_Load_Stack": "An error occurred while loading the stack \"{{stackId}}\".", "An error occurred while marking this stack as open.": "An error occurred while marking this stack as open.", "An error occurred while marking this stack as discarded.": "An error occurred while marking this stack as discarded.", "Are you sure you want to all current stack events and discard any future stack events?": "Are you sure you want to all current stack events and discard any future stack events?", "All future occurrences will be discarded and will not count against your event limit.": "All future occurrences will be discarded and will not count against your event limit.", "An error occurred while marking future occurrences as not critical.": "An error occurred while marking future occurrences as not critical.", "An error occurred while marking future occurrences as critical.": "An error occurred while marking future occurrences as critical.", "Successfully marked the stack as open.": "Successfully marked the stack as open.", "Successfully marked the stack as fixed.": "Successfully marked the stack as fixed.", "Successfully marked the stacks as ignored.": "Successfully marked the stacks as ignored.", "An error occurred while marking this stack as fixed.": "An error occurred while marking this stack as fixed.", "An error occurred while marking this stack as snoozed.": "An error occurred while marking this stack as snoozed.", "An error occurred while marking this stack as ignored.": "An error occurred while marking this stack as ignored.", "An error occurred while marking stacks as ignored.": "An error occurred while marking stacks as ignored.", "Successfully marked the stacks as open.": "Successfully marked the stacks as open.", "An error occurred while marking stacks as open.": "An error occurred while marking stacks as open.", "Successfully marked the stacks as discarded.": "Successfully marked the stacks as discarded.", "An error occurred while marking stacks as discarded.": "An error occurred while marking stacks as discarded.", "Mark Open": "Mark Open", "Mark Stack Open": "<PERSON>", "Mark Stack Discarded": "<PERSON> Discarded", "Mark Stack Fixed": "<PERSON> Fi<PERSON>", "Fixed_With_Version": "FIXED IN {{fixed_in_version}}", "Opening": "Opening", "Discard": "Discard", "open": "Open", "discarded": "Discarded", "fixed": "Fixed", "regressed": "Regressed", "snoozed": "Snoozed", "ignored": "Ignored", "6 Hours": "6 Hours", "1 Day": "1 Day", "1 Week": "1 Week", "1 Month": "1 Month", "All Projects": "All Projects", "Loading Organizations...": "Loading Organizations...", "Loading Projects...": "Loading Projects...", "Add New Project": "Add New Project", "Last Hour": "Last Hour", "Last 24 Hours": "Last 24 Hours", "Last Week": "Last Week", "Last 30 Days": "Last 30 Days", "All Time": "All Time", "Custom": "Custom", "Select Date Range": "Select Date Range", "In Date Range": "In Date Range", "DayFormat": "D", "DateFormat": "ddd, MMM D, YYYY", "DateTimeFormat": "ddd, MMM D, YYYY h:mma", "Filter": "Filter", "Search Documentation": "Search Documentation", "My Projects": "My Projects", "Project Information": "Project Information", "Organization Name": "Organization Name", "New Organization Name": "New Organization Name", "Project Name": "Project Name", "New Project Name": "New Project Name", "Add Project": "Add Project", "Manage Project": "Manage Project", "General": "General", "API Keys": "API Keys", "Settings": "Settings", "Client Configuration": "Client Configuration", "Integrations": "Integrations", "Download & Configure Client": "Download & Configure Client", "Manage Organization": "Manage Organization", "Manage Notification Settings": "Manage Notification Settings", "Reset Project Data": "Reset Project Data", "Delete Project": "Delete Project", "API Key": "API Key", "Enable": "Enable", "Disable": "Disable", "Are you sure you want to enable the API key?": "Are you sure you want to enable the API key?", "ENABLE API KEY": "ENABLE API KEY", "An error occurred while enabling the API key.": "An error occurred while enabling the API key.", "Are you sure you want to disable the API key?": "Are you sure you want to disable the API key?", "DISABLE API KEY": "DISABLE API KEY", "An error occurred while disabling the API key.": "An error occurred while disabling the API key.", "Notes": "Notes", "Payment Number": "Payment Number", "Actions": "Actions", "Status": "Status", "This project does not have an API Key.": "This project does not have an API Key.", "New API Key": "New API Key", "New Client Configuration": "New Client Configuration", "Event Types": "Event Types", "Add Web Hook": "Add Web Hook", "Connect Zapier": "Connect Zapier", "Data Exclusions": "Data Exclusions", "User Namespaces": "User Namespaces", "Error Stacking": "<PERSON><PERSON><PERSON>", "Common Methods": "Common Methods", "Spam Detection": "Spam Detection", "Please enter a configuration setting": "Please enter a configuration setting", "Name": "Name", "Organization": "Organization", "Stacks": "Stacks", "Events": "Events", "View Organization": "View Organization", "Key": "Key", "Project Name is required.": "Project Name is required.", "A project with this name already exists.": "A project with this name already exists.", "This project does not contain any custom configuration settings.": "This project does not contain any custom configuration settings.", "NOTICE_CONFIGURATION": "The <a href=\"https://exceptionless.com/docs/project-settings/#client-configuration\" target=\"_blank\">configuration values</a> on this page will be sent to the Exceptionless clients in real time. This allows you to change how your app works without redeploying your app.", "No projects were found with the current filter.": "No projects were found with the current filter.", "No projects were found.": "No projects were found.", "Notice_Data_Exclusions": "A comma delimited list of field names that should be removed from any error report data (e.g., extended data properties, form fields, cookies and query parameters). You can also specify a <a href=\"https://exceptionless.com/docs/security/\" target=\"_blank\" title=\"Data Exclusions documentation\">field name with wildcards (<strong>*</strong>)</a> to specify starts with, ends with, or contains just to be extra safe.", "Automatically remove user identifiable information from events (e.g., Machine Name, User Information, IP Addresses and more...).": "Automatically remove user identifiable information from events (e.g., Machine Name, User Information, IP Addresses and more...).", "Notice_Error_Stacking": "A comma delimited list of the namespace names that your applications code belongs to. If this value is set, only methods inside of these namespaces will be considered as stacking targets.", "Notice_Common_Methods": "A comma delimited list of common method names that should not be used as stacking targets. This is useful when your code contains shared utility methods that throw a lot of errors.", "Notice_Spam_Detection": "A comma delimited list of user agents that should be ignored.", "Example:": "Example:", "Reduce noise by automatically hiding high volumes of events coming from a single client IP address.": "Reduce noise by automatically hiding high volumes of events coming from a single client IP address.", "Please enter a valid key.": "Please enter a valid key.", "Please enter a valid value.": "Please enter a valid value.", "Configuration Key": "Configuration Key", "Configuration Value": "Configuration Value", "to enable integrations!": "to enable integrations!", "Choose how often you want to receive slack notifications for event occurrences in this project.": "Choose how often you want to receive slack notifications for event occurrences in this project.", "(Coming soon!)": "(Coming soon!)", "Add Slack Notifications": "Add Slack Notifications", "Remove Slack": "<PERSON><PERSON><PERSON>", "Notice_Web_hooks": "The following web hooks will be called for this project.", "Notice_Zapier": "Exceptionless has a native Zapier integration. You can use Zapier to connect your Exceptionless account to over 3,000 other applications all without writing any code.", "This project does not contain any integrations.": "This project does not contain any integrations.", "An error occurred while loading your projects.": "An error occurred while loading your projects.", "Are you sure you want to delete this project?": "Are you sure you want to delete this project?", "Successfully queued the project for deletion.": "Successfully queued the project for deletion.", "An error occurred while trying to remove the project.": "An error occurred while trying to remove the project.", "Download_Configure_Project": "Download & Configure Project {{projectName}} Client", "The Exceptionless client can be integrated into your project in just a few easy steps.": "The Exceptionless client can be integrated into your project in just a few easy steps.", "Select your project type:": "Select your project type:", "Please select a project type": "Please select a project type", "Execute the following in your shell:": "Execute the following in your shell:", "Add the script to your HTML page:": "Add the script to your HTML page:", "Configure the ExceptionlessClient with your Exceptionless API key:": "Configure the ExceptionlessClient with your Exceptionless API key:", "Notice_Install_Nuget_Package": "Add the Exceptionless <a href=\"http://nuget.org\" target=\"_blank\">NuGet</a> package to your Visual Studio project by running this command in the <a href=\"http://docs.nuget.org/docs/start-here/using-the-package-manager-console\">Package Manager Console</a>:", "Notice_Install_Manually": "If you wish to install manually or from a CDN please view the <a href=\"https://github.com/exceptionless/Exceptionless.JavaScript\" target=\"_blank\">readme</a>.", "Notice_Install_Node_Package": "Install Exceptionless using the <a href=\"https://www.npmjs.com\" target=\"_blank\">Node Package Manager</a>:", "Notice_Update_ApiKey": "Update the <code>&lt;exceptionless apiKey=\"API_KEY_HERE\" /&gt;</code> section located in the project's {{config}} with your Exceptionless API key:", "Notice_Application_Startup_NetFx": "Import the <code>Exceptionless</code> namespace and call the <code>ExceptionlessClient.Default.Startup(\"{{apiKey}}\")</code> method with your api key during the startup of your app.", "Notice_Application_Startup_NetCore": "Import the <code>Exceptionless</code> namespace and call the <code>services.AddExceptionless(\"{{api<PERSON>ey}}\")</code> method inside of the ConfigureServices method. Next, call the <code>app.UseExceptionless()</code> method in the Configure during the startup of your app.", "Notice_Application_Startup_Nancy": "Import the <code>Exceptionless</code> namespace and call the <code>Exceptionless.ExceptionlessClient.Default.RegisterNancy(pipelines)</code> method with an instance of Nancy.Bootstrapper.IPipelines inside of your Nancy Bootstrapper's ApplicationStartup method.", "Notice_Application_Startup_Windows": "Import the <code>Exceptionless</code> namespace and call the <code>ExceptionlessClient.Default.Register()</code> method during the startup of your app.", "Notice_Application_Startup_WebApi": "Import the <code>Exceptionless</code> namespace and call the <code>ExceptionlessClient.Default.RegisterWebApi(config)</code> method with an instance of HttpConfiguration during the startup of your app.", "Notice_Hosting_WebApi_In_AspNet": "If you are hosting Web API inside of ASP.NET, you would register Exceptionless like: <code>Exceptionless.ExceptionlessClient.Default.RegisterWebApi(GlobalConfiguration.Configuration)</code>", "Notice_Unhandled_Exception": "If you are targeting Net Standard or Portable Class Libraries (PCL) you may need to wire up to any unhandled exception events specific to your project type to start reporting them! Please see the <a target=\"_blank\" href=\"https://exceptionless.com/docs/clients/dotnet/\">Exceptionless docs</a> for more information.", "That's it!": "That's it!", "Your project should now automatically be sending all unhandled exceptions to Exceptionless!": "Your project should now automatically be sending all unhandled exceptions to Exceptionless!", "Notice_Submit_Exception": "You can also <a href=\"{{docUrl}}\" target=\"_blank\">send handled exceptions to Exceptionless</a> using <code>{{sampleCode}}</code>.", "Notice_CommandLine_Application": "For more information and troubleshooting tips, view the <a target=\"_blank\" href=\"https://exceptionless.com/docs/api/\">Exceptionless docs</a>.", "Notice_DotNet_Application": "For more information and troubleshooting tips, view the <a target=\"_blank\" href=\"https://exceptionless.com/docs/clients/dotnet/\">Exceptionless docs</a>.", "Notice_JavaScript_Application": "For more information and platform specific integrations (E.G., AngularJS, Express.js, React, Vue) view the <a target=\"_blank\" href=\"https://exceptionless.com/docs/clients/javascript/\">Exceptionless docs</a>.", "Copy not supported.": "<PERSON><PERSON> not supported.", "Press ⌘-C to copy.": "Press ⌘-C to copy.", "Press Ctrl-C to copy.": "Press Ctrl-C to copy.", "An error occurred while getting the API key for your project.": "An error occurred while getting the API key for your project.", "Console and Service applications": "Console and Service applications", "Browser applications": "Browser applications", "Cannot_Find_Project": "The project \"{{projectId}}\" could not be found.", "An error occurred while creating the organization.": "An error occurred while creating the organization.", "An error occurred while creating the project.": "An error occurred while creating the project.", "<New Organization>": "<New Organization>", "Add to Slack": "Add to Slack", "Project Most Frequent": "Most Frequent", "Please upgrade your plan to enable slack integration.": "Please upgrade your plan to enable slack integration.", "An error occurred while adding Slack to your project.": "An error occurred while adding Slack to your project.", "An error occurred while creating a new API key for your project.": "An error occurred while creating a new API key for your project.", "An error occurred while saving the configuration setting.": "An error occurred while saving the configuration setting.", "An error occurred loading the api keys.": "An error occurred loading the api keys.", "An error occurred loading the notification settings.": "An error occurred loading the notification settings.", "An error occurred while loading the slack notification settings.": "An error occurred while loading the slack notification settings.", "Are you sure you want to delete this configuration setting?": "Are you sure you want to delete this configuration setting?", "DELETE CONFIGURATION SETTING": "DELETE CONFIGURATION SETTING", "An error occurred while trying to delete the configuration setting.": "An error occurred while trying to delete the configuration setting.", "An error occurred while trying to delete the project.": "An error occurred while trying to delete the project.", "Are you sure you want to remove slack support?": "Are you sure you want to remove slack support?", "An error occurred while trying to remove slack.": "An error occurred while trying to remove slack.", "Are you sure you want to delete this API key?": "Are you sure you want to delete this API key?", "DELETE API KEY": "DELETE API KEY", "An error occurred while trying to delete the API Key.": "An error occurred while trying to delete the API Key.", "Are you sure you want to delete this web hook?": "Are you sure you want to delete this web hook?", "DELETE WEB HOOK": "DELETE WEB HOOK", "An error occurred while trying to delete the web hook.": "An error occurred while trying to delete the web hook.", "Are you sure you want to reset the data for this project?": "Are you sure you want to reset the data for this project?", "RESET PROJECT DATA": "RESET PROJECT DATA", "An error occurred while resetting project data.": "An error occurred while resetting project data.", "An error occurred while saving the project.": "An error occurred while saving the project.", "An error occurred while saving the API key note.": "An error occurred while saving the API key note.", "An error occurred while saving the common methods.": "An error occurred while saving the common methods.", "An error occurred while saving the data exclusion.": "An error occurred while saving the data exclusion.", "An error occurred while saving the include private information setting.": "An error occurred while saving the include private information setting.", "An error occurred while saving the user agents.": "An error occurred while saving the user agents.", "An error occurred while saving the user namespaces.": "An error occurred while saving the user namespaces.", "An error occurred while saving your slack notification settings.": "An error occurred while saving your slack notification settings.", "Project_Deleted": "The project \"{{projectId}}\" was deleted.", "My Organizations": "My Organizations", "Plan": "Plan", "Projects": "Projects", "View Invoices": "View Invoices", "Leave Organization": "Leave Organization", "New Organization": "New Organization", "Monthly Usage": "Monthly Usage", "Invite User": "Invite User", "Delete Organization": "Delete Organization", "UserManager": "User", "Billing": "Billing", "Total number of projects": "Total number of projects", "Total number of stacks": "Total number of stacks", "Total number of events within the retention period": "Total number of events within the retention period", "No organizations were found with the current filter.": "No organizations were found with the current filter.", "No organizations were found.": "No organizations were found.", "View Payment": "View Payment", "View Stripe Invoice": "View Stripe Invoice", "No invoices were found.": "No invoices were found.", "Organization Name is required.": "Organization Name is required.", "A organizations with this name already exists.": "A organizations with this name already exists.", "The usage data above is refreshed periodically and may not reflect current totals.": "The usage data above is refreshed periodically and may not reflect current totals.", "Click here to change your plan or billing information.": "Click here to change your plan or billing information.", "Change your plan or billing information.": "Change your plan or billing information.", "Organization_Billing_Plan": "You are currently on the <strong>{{planName}}</strong> plan.", "An error occurred while loading your organizations.": "An error occurred while loading your organizations.", "Cannot_Find_Organization": "The organization \"{{organizationId}}\" could not be found.", "An error occurred while inviting the user.": "An error occurred while inviting the user.", "Organization_Deleted": "The organization \"{{organizationId}}\" was deleted.", "Are you sure you want to leave this organization?": "Are you sure you want to leave this organization?", "An error occurred while trying to leave the organization.": "An error occurred while trying to leave the organization.", "Are you sure you want to delete this organization?": "Are you sure you want to delete this organization?", "Successfully queued the organization for deletion.": "Successfully queued the organization for deletion.", "An error occurred while trying to delete the organization.": "An error occurred while trying to delete the organization.", "An error occurred while saving the organization.": "An error occurred while saving the organization.", "Configure_Project_Title": "Configure {{projectName}} ({{organizationName}})", "Invoice #": "Invoice #", "Invoice Date": "Invoice Date", "Invoice": "Invoice", "Receipt": "Receipt", "Paid": "Paid", "Unpaid": "Unpaid", "Amount": "Amount", "Email:": "Email:", "Website:": "Website:", "Organization_Payment_Plan": "<strong>{{organization}}</strong> is currently on the <strong>{{planName}}</strong> plan.", "Payment_Select_New_Plan": "Select new plan (<a href=\"https://exceptionless.com/pricing\" target=\"_blank\">view plan information</a>). <em>All plan changes are prorated.</em>", "Credit Card": "Credit Card", "Coupon code": "Coupon code", "Exceptionless Plan": "Exceptionless Plan", "Organization to change": "Organization to change", "Use a new credit card": "Use a new credit card", "Card number": "Card number", "Expires": "Expires", "Name on card": "Name on card", "Card code": "Card code", "Help us improve Exceptionless!": "Help us improve Exceptionless!", "Contact Us": "Contact Us", "Changing Plan": "Changing Plan", "Please contact support for more information.": "Please contact support for more information.", "Cannot_Find_Invoice": "The invoice \"{{invoiceId}}\" could not be found.", "An error occurred while changing plans.": "An error occurred while changing plans.", "Thanks! Your billing plan has been successfully changed.": "Thanks! Your billing plan has been successfully changed.", "An error occurred while loading available billing plans.": "An error occurred while loading available billing plans.", "An error occurred while loading your user account.": "An error occurred while loading your user account.", "Credit_Card_Ending": "Use credit card ending in {{card_last4}}", "Email": "Email", "Full Name": "Full Name", "Email Address": "Email Address", "Email address (no spam)": "Email address (no spam)", "Notifications": "Notifications", "Password": "Password", "Current Password": "Current Password", "Current Password is required.": "Current Password is required.", "New Password": "New Password", "New Password is required.": "New Password is required.", "New Password must be at least 6 characters long.": "New Password must be at least 6 characters long.", "New Password must be less than 101 characters long.": "New Password must be less than 101 characters long.", "Confirm password": "Confirm password", "New Password and Confirmation Password fields do not match.": "New Password and Confirmation Password fields do not match.", "Confirm Password is required.": "Confirm Password is required.", "Confirm Password must be at least 6 characters long.": "Confirm Password must be at least 6 characters long.", "Confirm Password must be less than 101 characters long.": "Confirm Password must be less than 101 characters long.", "Set Password": "Set Password", "Changing Password": "Changing Password", "DELETE ACCOUNT": "DELETE ACCOUNT", "External Logins": "External Logins", "Add an external login": "Add an external login", "Existing external logins": "Existing external logins", "Remove": "Remove", "No external logins were found.": "No external logins were found.", "Log in using your Microsoft account": "Log in using your Microsoft account", "Log in using your Google account": "Log in using your Google account", "Log in using your Facebook account": "Log in using your Facebook account", "Log in using your GitHub account": "Log in using your GitHub account", "An error occurred while adding external login.": "An error occurred while adding external login.", "An error occurred while loading the notification settings.": "An error occurred while loading the notification settings.", "An error occurred while loading the projects.": "An error occurred while loading the projects.", "An error occurred while loading your user profile.": "An error occurred while loading your user profile.", "Are you sure you want to delete your account?": "Are you sure you want to delete your account?", "Successfully removed your user account.": "Successfully removed your user account.", "An error occurred while trying remove your user account.": "An error occurred while trying remove your user account.", "An error occurred while sending your verification email.": "An error occurred while sending your verification email.", "An error occurred while saving your email address.": "An error occurred while saving your email address.", "An error occurred while saving your notification settings.": "An error occurred while saving your notification settings.", "An error occurred while saving your email notification preferences.": "An error occurred while saving your email notification preferences.", "An error occurred while saving your full name.": "An error occurred while saving your full name.", "An error occurred while removing the external login.": "An error occurred while removing the external login.", "Choose how often you want to receive notifications for event occurrences in this project.": "Choose how often you want to receive notifications for event occurrences in this project.", "Are you sure you want to add the admin role for this user?": "Are you sure you want to add the admin role for this user?", "Are you sure you want to remove the admin role from this user?": "Are you sure you want to remove the admin role from this user?", "An error occurred while add the admin role.": "An error occurred while add the admin role.", "An error occurred while remove the admin role.": "An error occurred while remove the admin role.", "Log in": "Log in", "after": "after", "before": "before", "with": "with", "OR": "OR", "Login with": "Login with", "Signup": "Signup", "Forgot password?": "Forgot password?", "Create an account": "Create an account", "Create My Account": "Create My Account", "Log In": "Log In", "Change password": "Change password", "Reset your password": "Reset your password", "Forgot Password": "Forgot Password", "NOTICE_SIGNINGUP": "By signing up, you agree to our <a href=\"https://exceptionless.com/privacy\" target=\"_blank\">Privacy Policy</a> and <a href=\"https://exceptionless.com/terms\" target=\"_blank\">Terms of Service</a>.", "Loggin_Failed_Message": "An error occurred while logging in. Please contact support for more information.", "Unable_to_connect_to": "Unable to connect to", "ResetPassword_Failed_Message": "An error occurred while trying to reset your password.", "ResetPassword_Success_Message": "An email was sent that contains instructions to change your password.", "An error occurred while signing up.  Please contact support for more information.": "An error occurred while signing up.  Please contact support for more information.", "You have successfully changed your password.": "You have successfully changed your password.", "An error occurred while trying to change your password.": "An error occurred while trying to change your password.", "Successfully verified your account.": "Successfully verified your account.", "An error occurred while verifying your account.": "An error occurred while verifying your account.", "AdminRole": "Admin", "Invited": "Invited", "Resend": "Resend", "Your first and last name": "Your first and last name", "verification email.": "verification email.", "Email not verified.": "Email not verified.", "Email notifications are currently disabled.": "Email notifications are currently disabled.", "To enable email notifications you must first verify your email address.": "To enable email notifications you must first verify your email address.", "Enable email notifications": "Enable email notifications", "Send daily project summary": "Send daily project summary", "Notify me on new errors": "Notify me on new errors", "Notify me on critical errors": "Notify me on critical errors", "Notify me on error regressions": "Notify me on error regressions", "Notify me on new events": "Notify me on new events", "Notify me on critical events": "Notify me on critical events", "Upgrade now": "Upgrade now", "to enable occurrence level notifications!": "to enable occurrence level notifications!", "Resend Invite Email": "Resend Invite <PERSON>", "Revoke Invite": "Revoke Invite", "Remove User": "Remove User", "Add Admin Role": "Add Admin Role", "Remove Admin Role": "Remove <PERSON><PERSON>", "No users were found.": "No users were found.", "Are you sure you want to remove this user from your organization?": "Are you sure you want to remove this user from your organization?", "An error occurred while trying to remove the user.": "An error occurred while trying to remove the user.", "An error occurred while trying to resend the notification.": "An error occurred while trying to resend the notification.", "Create New Web Hook": "Create New Web Hook", "Url": "Url", "Enter the URL to call": "Enter the URL to call", "URL is required.": "URL is required.", "Control when the web hook is called by choosing the event types below.": "Control when the web hook is called by choosing the event types below.", "Please choose one or more event types.": "Please choose one or more event types.", "Create Web Hook": "Create Web Hook", "New Error": "New Error", "Occurs when a new error that has never been seen before is reported to your project.": "Occurs when a new error that has never been seen before is reported to your project.", "Critical Error": "Critical Error", "Occurs when an error that has been marked as critical is reported to your project.": "Occurs when an error that has been marked as critical is reported to your project.", "Regression": "Regression", "Occurs when an event that has been marked as fixed has reoccurred in your project.": "Occurs when an event that has been marked as fixed has reoccurred in your project.", "Occurs when a new event that has never been seen before is reported to your project.": "Occurs when a new event that has never been seen before is reported to your project.", "Critical Event": "Critical Event", "Occurs when an event that has been marked as critical is reported to your project.": "Occurs when an event that has been marked as critical is reported to your project.", "Promoted": "Promoted", "Used to promote event stacks to external systems.": "Used to promote event stacks to external systems.", "Delete": "Delete", "Cancel": "Cancel", "Apply": "Apply", "DateTime": "{{date | date: 'medium'}}", "Go To Most Frequent": "Go To Most Frequent", "Save": "Save", "Edit": "Edit", "to": "to", "Email Address is required.": "Email Address is required.", "Password is required": "Password is required", "Password must be at least 6 characters long": "Password must be at least 6 characters long", "Password must be less than 101 characters long": "Password must be less than 101 characters long", "Signup for a FREE account in seconds": "Signup for a FREE account in seconds", "Full Name is required.": "Full Name is required.", "A user already exists with this email address.": "A user already exists with this email address.", "Already have an account?": "Already have an account?", "API rate limit exceeded": "API rate limit exceeded", "Your API rate limit has been exceeded. Please try again in 15 minutes.": "Your API rate limit has been exceeded. Please try again in 15 minutes.", "to enable premium features and extra storage!": "to enable premium features and extra storage!", "to enable search and other premium features!": "to enable search and other premium features!", "to continue receiving events.": "to continue receiving events.", "to increase your limits.": "to increase your limits.", "is currently on a free plan.": "is currently on a free plan.", "has reached its monthly plan limit.": "has reached its monthly plan limit.", "Events are currently being throttled to prevent using up your plan limit in a small window of time.": "Events are currently being throttled to prevent using up your plan limit in a small window of time.", "Events are currently being throttled for": "Events are currently being throttled for", "API requests are currently being throttled for": "API requests are currently being throttled for", "We haven't received any data!": "We haven't received any data!", "Setup your first project": "Setup your first project", "Copied!": "Copied!", "Message:": "Message:", "Allowed": "Allowed", "Blocked": "Blocked", "Too Big": "Too Big", "Limit": "Limit", "Allowed in Organization": "Allowed in Organization", "Occurrences": "Occurrences", "Log source": "Log source", "Add": "Add", "Total": "Total", "Log Level": "Log Level", "trace": "Trace", "debug": "Debug", "info": "Info", "warn": "<PERSON><PERSON>", "error": "Error", "fatal": "Fatal", "off": "Off", "Default": "<PERSON><PERSON><PERSON>", "Notice_Default_Log_Level": "The default log level controls the minimum log level that should be accepted for log events. Log levels can also be overridden at the log stack level.", "DIALOGS_ERROR": "Error", "DIALOGS_ERROR_MSG": "An unknown error has occurred.", "DIALOGS_CLOSE": "Close", "DIALOGS_PLEASE_WAIT": "Please Wait", "DIALOGS_PLEASE_WAIT_ELIPS": "Please Wait...", "DIALOGS_PLEASE_WAIT_MSG": "Waiting on operation to complete.", "DIALOGS_PERCENT_COMPLETE": "% Complete", "DIALOGS_NOTIFICATION": "Notification", "DIALOGS_NOTIFICATION_MSG": "Unknown application notification.", "DIALOGS_CONFIRMATION": "Confirmation", "DIALOGS_CONFIRMATION_MSG": "Confirmation required.", "DIALOGS_JSON": "JSON", "DIALOGS_OK": "OK", "DIALOGS_YES": "Yes", "DIALOGS_NO": "No", "NOTICE_GRAVATAR": "Your avatar is generated by requesting a <a href=\"https://gravatar.com\" target=\"_blank\">Gravatar image</a> with the email address below."}