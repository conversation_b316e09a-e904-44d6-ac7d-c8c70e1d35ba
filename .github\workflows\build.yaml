name: Build
on: [ push, pull_request ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: Build Reason
      run: "echo ref: ${{github.ref}} event: ${{github.event_name}}"
    - name: Build Version
      run: |
        dotnet tool install --global minver-cli --version 2.5.0
        version=$(minver --tag-prefix v)
        echo "MINVERVERSIONOVERRIDE=$version" >> $GITHUB_ENV
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Build
      run: |
        docker build --build-arg UI_VERSION=${VERSION} --target ui -t ex-ui .
    - name: Run Tests
      run: |
        docker build --target testrunner -t exceptionless:test .
        docker run exceptionless:test
    - name: Log in to GitHub Packages
      run: echo ${{ secrets.GITHUB_TOKEN }} | docker login docker.pkg.github.com --username ${{ github.actor }} --password-stdin
    - name: Log in to Docker Hub
      if: github.event_name != 'pull_request'
      run: echo ${{ secrets.DOCKER_PASSWORD }} | docker login --username ${{ secrets.DOCKER_USERNAME }} --password-stdin
    - name: Push CI Packages
      if: github.event_name != 'pull_request'
      run: |
        # tag and push docker image
        for tag in {${VERSION},latest}; do
          docker tag ex-ui docker.pkg.github.com/exceptionless/exceptionless.ui/ui:$tag
          docker push docker.pkg.github.com/exceptionless/exceptionless.ui/ui:$tag
        done
        for tag in {${VERSION},latest}; do
          docker tag ex-ui exceptionless/ui-ci:$tag
          docker push exceptionless/ui-ci:$tag
        done
    - name: Publish Release Packages
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        # tag and push docker image
        for tag in {${VERSION},latest}; do
          docker tag ex-ui exceptionless/ui:$tag
          docker push exceptionless/ui:$tag
        done
    - name: Install Helm
      if: github.event_name != 'pull_request'
      uses: azure/setup-helm@v1
      with:
        version: v3.2.4
    - name: Checkout Development Helm Chart
      if: github.ref == 'refs/heads/feature/discard-join' && github.event_name != 'pull_request'
      run: |
        git clone https://github.com/exceptionless/Exceptionless.git helm
        cd helm
        git checkout $(echo ${GITHUB_REF#refs/heads/})
    - name: Deploy Changes to Development Environment
      if: github.ref == 'refs/heads/feature/discard-join' && github.event_name != 'pull_request'
      run: |
        az login --service-principal --username ${{ secrets.AZ_USERNAME }} --password ${{ secrets.AZ_PASSWORD }} --tenant ${{ secrets.AZ_TENANT }} --output none
        az aks get-credentials --resource-group exceptionless-v6 --name ex-k8s-v6
        helm upgrade --set "app.image.tag=${VERSION}" --reuse-values ex-dev --namespace ex-dev ./helm/k8s/exceptionless
    - name: Checkout Production Helm Chart
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        git clone https://github.com/exceptionless/Exceptionless.git helm
        cd helm
        git checkout main
    - name: Deploy Changes to Production Environment
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        az login --service-principal --username ${{ secrets.AZ_USERNAME }} --password ${{ secrets.AZ_PASSWORD }} --tenant ${{ secrets.AZ_TENANT }} --output none
        az aks get-credentials --resource-group exceptionless-v6 --name ex-k8s-v6
        helm upgrade --set "app.image.tag=${VERSION}" --reuse-values ex-prod --namespace ex-prod ./helm/k8s/exceptionless

