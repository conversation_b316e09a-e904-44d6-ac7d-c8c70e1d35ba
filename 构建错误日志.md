
PS D:\Work\lib\Exceptionless.UI\src> npm install
npm WARN read-shrinkwrap This version of npm is compatible with lockfileVersion@1, but package-lock.json was generated for lockfileVersion@2. I'll try to do my best with it!
npm WARN deprecated are-we-there-yet@1.1.5: This package is no longer supported.
npm WARN deprecated is-accessor-descriptor@1.0.0: Please upgrade to v1.0.1
npm WARN deprecated is-data-descriptor@1.0.0: Please upgrade to v1.0.1
npm WARN deprecated bower@1.8.8: We don't recommend using Bower for new projects. Please consider Yarn and Webpack or Parcel. You can read how to migrate legacy project here: https://bower.io/blog/2017/how-to-migrate-away-from-bower/
npm WARN deprecated glob@7.1.4: Glob versions prior to v9 are no longer supported
npm WARN deprecated coffee-script@1.10.0: CoffeeScript on NPM has moved to "coffeescript" (no hyphen)
npm WARN deprecated coffee-script@1.12.7: CoffeeScript on NPM has moved to "coffeescript" (no hyphen)
npm WARN deprecated CSSselect@0.7.0: the module is now available as 'css-select'
npm WARN deprecated CSSwhat@0.4.7: the module is now available as 'css-what'
npm WARN deprecated date-format@2.1.0: 2.x is no longer supported. Please upgrade to 4.x or higher.
npm WARN deprecated domelementtype@1.3.0: update to domelementtype@1.3.1
npm WARN deprecated glob@5.0.15: Glob versions prior to v9 are no longer supported
npm WARN deprecated debug@3.2.6: Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)
npm WARN deprecated formidable@1.2.1: Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau
npm WARN deprecated gauge@2.7.4: This package is no longer supported.
npm WARN deprecated debug@4.1.1: Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)
npm WARN deprecated glob@7.0.6: Glob versions prior to v9 are no longer supported
npm WARN deprecated is-accessor-descriptor@0.1.6: Please upgrade to v0.1.7
npm WARN deprecated is-data-descriptor@0.1.4: Please upgrade to v0.1.5
npm WARN deprecated fsevents@1.2.9: Upgrade to fsevents v2 to mitigate potential security issues  
npm WARN deprecated rimraf@2.6.3: Rimraf versions prior to v4 are no longer supported
npm WARN deprecated rimraf@2.7.1: Rimraf versions prior to v4 are no longer supported
npm WARN deprecated grunt-ng-annotate@3.0.0: grunt-ng-annotate is deprecated. Switch to babel-plugin-angularjs-annotate or provide annotations by yourself.
npm WARN deprecated har-validator@5.1.3: this library is no longer supported
npm WARN deprecated iltorb@2.4.5: The zlib module provides APIs for brotli compression/decompression starting with Node.js v10.16.0, please use it over iltorb
npm WARN deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm WARN deprecated ini@1.3.5: Please update to ini >=1.3.6 to avoid a prototype pollution issue  
npm WARN deprecated glob@7.1.6: Glob versions prior to v9 are no longer supported
npm WARN deprecated log4js@4.5.1: 4.x is no longer supported. Please upgrade to 6.x or higher.
npm WARN deprecated mkdirp@0.5.1: Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)
 to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)
npm WARN deprecated npmlog@4.1.2: This package is no longer supported.
npm WARN deprecated npmlog@4.1.2: This package is no longer supported.
npm WARN deprecated puppeteer@2.1.1: < 22.8.2 is no longer supported
npm WARN deprecated puppeteer@2.1.1: < 22.8.2 is no longer supported
npm WARN deprecated q@0.9.3: You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.
npm WARN deprecated
npm WARN deprecated (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)   
npm WARN deprecated q@1.5.1: You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.
npm WARN deprecated
gainst the odds. Be excellent to each other.
npm WARN deprecated
npm WARN deprecated
npm WARN deprecated (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)   
npm WARN deprecated (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)   
npm WARN deprecated querystring@0.2.0: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.
npm WARN deprecated request@2.88.0: request has been deprecated, see https://github.com/request/request/issues/3142
npm WARN deprecated resolve-url@0.2.1: https://github.com/lydell/resolve-url#deprecated
npm WARN deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm WARN deprecated socks@1.1.10: If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0
npm WARN deprecated source-map-resolve@0.5.2: See https://github.com/lydell/source-map-resolve#deprecated
npm WARN deprecated querystring@0.2.0: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.
npm WARN deprecated request@2.88.0: request has been deprecated, see https://github.com/request/request/issues/3142
npm WARN deprecated resolve-url@0.2.1: https://github.com/lydell/resolve-url#deprecated
npm WARN deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm WARN deprecated socks@1.1.10: If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0
npm WARN deprecated source-map-resolve@0.5.2: See https://github.com/lydell/source-map-resolve#depquest/issues/3142
npm WARN deprecated resolve-url@0.2.1: https://github.com/lydell/resolve-url#deprecated
npm WARN deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm WARN deprecated socks@1.1.10: If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0
npm WARN deprecated socks@1.1.10: If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0
npm WARN deprecated source-map-resolve@0.5.2: See https://github.com/lydell/source-map-resolve#deprecated
npm WARN deprecated source-map-resolve@0.5.2: See https://github.com/lydell/source-map-resolve#deprecated
npm WARN deprecated source-map-url@0.4.0: See https://github.com/lydell/source-map-url#deprecated 
his library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility
npm WARN deprecated streamroller@1.0.6: 1.x is no longer supported. Please upgrade to 3.x or higher.
npm WARN deprecated superagent@3.8.3: Please upgrade to v9.0.0+ as we have fixed a public vulnerability with formidable dependency. Note that v9.0.0+ requires Node.js v14.18.0+. See https://github.com/ladjs/superagent/pull/1800 for insight. This project is supported and maintained by the team at Forward Email @ https://forwardemail.net
npm WARN deprecated urix@0.1.0: Please see https://github.com/lydell/urix#deprecated
npm WARN deprecated uuid@3.3.3: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.

> iltorb@2.4.5 install D:\Work\lib\Exceptionless.UI\src\node_modules\iltorb
> node ./scripts/install.js || node-gyp rebuild

info looking for cached prebuild @ C:\Users\<USER>\AppData\Roaming\npm-cache\_prebuilds\c113e5-iltorb-v2.4.5-node-v72-win32-x64.tar.gz        
http request GET https://github.com/nstepien/iltorb/releases/download/v2.4.5/iltorb-v2.4.5-node-v72-win32-x64.tar.gz
http 200 https://github.com/nstepien/iltorb/releases/download/v2.4.5/iltorb-v2.4.5-node-v72-win32-x64.tar.gz
info downloading to @ C:\Users\<USER>\AppData\Roaming\npm-cache\_prebuilds\c113e5-iltorb-v2.4.5-node-v72-win32-x64.tar.gz.34924-043b415b34ddd.tmp
info renaming to @ C:\Users\<USER>\AppData\Roaming\npm-cache\_prebuilds\c113e5-iltorb-v2.4.5-node-v72-win32-x64.tar.gz
info unpacking @ C:\Users\<USER>\AppData\Roaming\npm-cache\_prebuilds\c113e5-iltorb-v2.4.5-node-v72-win32-x64.tar.gz
info unpack resolved to D:\Work\lib\Exceptionless.UI\src\node_modules\iltorb\build\bindings\iltorb.node
info unpack required D:\Work\lib\Exceptionless.UI\src\node_modules\iltorb\build\bindings\iltorb.node successfully
info install Successfully installed iltorb binary!

> ws@0.4.32 install D:\Work\lib\Exceptionless.UI\src\node_modules\ws
> (node-gyp rebuild 2> builderror.log) || (exit 0)


D:\Work\lib\Exceptionless.UI\src\node_modules\ws>if not defined npm_config_node_gyp (node "C:\Users\<USER>\AppData\Roaming\nvm\v12.22.12\node_modules\npm\node_modules\npm-lifecycle\node-gyp-bin\\..\..\node_modules\node-gyp\bin\node-gyp.js" rebuild )  else (node "C:\Users\<USER>\AppData\Roaming\nvm\v12.22.12\node_modules\npm\node_modules\node-gyp\bin\node-gyp.js" rebuild )

> puppeteer@2.1.1 install D:\Work\lib\Exceptionless.UI\src\node_modules\puppeteer
> node install.js

ERROR: Failed to download Chromium r722234! Set "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD" env variable to skip download.
Error: connect ETIMEDOUT ***************:443
    at TCPConnectWrap.afterConnect [as oncomplete] (net.js:1144:16)
  -- ASYNC --
    at BrowserFetcher.<anonymous> (D:\Work\lib\Exceptionless.UI\src\node_modules\puppeteer\lib\helper.js:111:15)
    at Object.<anonymous> (D:\Work\lib\Exceptionless.UI\src\node_modules\puppeteer\install.js:66:16)
    at Module._compile (internal/modules/cjs/loader.js:999:30)
    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1027:10)
    at Module.load (internal/modules/cjs/loader.js:863:32)
    at Function.Module._load (internal/modules/cjs/loader.js:708:14)
    at Function.executeUserEntryPoint [as runMain] (internal/modules/run_main.js:60:12)
    at internal/main/run_main_module.js:17:47 {
  errno: 'ETIMEDOUT',
  code: 'ETIMEDOUT',
  syscall: 'connect',
  address: '***************',
  port: 443
}
npm WARN grunt-dom-munger@3.4.0 requires a peer of grunt@~0.4.1 but none is installed. You must install peer dependencies yourself.
npm WARN optional SKIPPING OPTIONAL DEPENDENCY: fsevents@1.2.9 (node_modules\glob-watcher\node_modules\fsevents):
npm WARN notsup SKIPPING OPTIONAL DEPENDENCY: Unsupported platform for fsevents@1.2.9: wanted {"os":"darwin","arch":"any"} (current: {"os":"win32","arch":"x64"})
npm WARN optional SKIPPING OPTIONAL DEPENDENCY: fsevents@2.1.2 (node_modules\fsevents):
npm WARN notsup SKIPPING OPTIONAL DEPENDENCY: Unsupported platform for fsevents@2.1.2: wanted {"os":"darwin","arch":"any"} (current: {"os":"win32","arch":"x64"})

npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! puppeteer@2.1.1 install: `node install.js`
npm ERR! Exit status 1
npm ERR!
npm ERR! Failed at the puppeteer@2.1.1 install script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.

npm ERR! A complete log of this run can be found in:
npm ERR!     C:\Users\<USER>\AppData\Roaming\npm-cache\_logs\2025-06-23T06_11_28_873Z-debug.log 