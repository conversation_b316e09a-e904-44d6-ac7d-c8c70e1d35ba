.bg-gd {
  #gradient > .vertical(rgba(40,50,60,0), rgba(40,50,60,0.075), 0, 100%);
  filter:none;
}

.bg-gd-dk {
  #gradient > .vertical(rgba(40,50,60,0), rgba(40,50,60,0.5), 10%, 100%);
  filter:none;
}

.bg-white-opacity {
	background-color: rgba(255, 255, 255, 0.5);
}
.bg-black-opacity {
	background-color: rgba(32, 43, 54, 0.5);
}

.bg-light {
	.color-variant(@brand-light, 2%, 3%, 3%, 5%);
	color: @text-color;
}

.bg-dark {
	.color-variant(@brand-dark, 5%, 10%, 5%, 10%);
	.font-variant(@brand-dark, lighten(@brand-dark,40%), lighten(@brand-dark,50%), #fff);
}

.bg-dark .nav > li > a {
  color: #e1e1e1;
}

.bg-dark .nav > li:hover > a,
.bg-dark .nav > li:focus > a {
  background-color: #4E8018;
}

.bg-dark .nav > li.active > a {
  background-color: #494545;
}

.bg-dark .nav.nav-sub > li.active > a {
  background-color: #64A500;
}

.bg-black {
	.color-variant(@brand-black, 5%, 10%, 5%, 10%);
	.font-variant(@brand-black, lighten(@brand-black,40%), lighten(@brand-black,50%), #fff);
}

.bg-primary {
	.color-variant(@brand-primary, 5%, 10%, 5%, 10%);
	.font-variant(@brand-primary, lighten(@brand-primary,40%), lighten(@brand-primary,50%), #fff);
}

.bg-success {
	.color-variant(@brand-success, 5%, 10%, 5%, 10%);
	.font-variant(@brand-success, lighten(@brand-success,40%), lighten(@brand-success,50%), #fff);
}

.bg-info {
	.color-variant(@brand-info, 5%, 10%, 5%, 10%);
	.font-variant(@brand-info, lighten(@brand-info,40%), lighten(@brand-info,50%), #fff);
}

.bg-warning {
	.color-variant(@brand-warning, 5%, 10%, 5%, 10%);
	.font-variant(@brand-warning, lighten(@brand-warning,40%), lighten(@brand-warning,50%), #fff);
}

.bg-danger {
	.color-variant(@brand-danger, 5%, 10%, 5%, 10%);
	.font-variant(@brand-danger, lighten(@brand-danger,40%), lighten(@brand-danger,50%), #fff);
}

.bg-white {
	background-color: #fff;
	color: @text-color;
	a {
		color: @link-color;
		&:hover{
			color: darken(@link-color, 10%);
		}
	}
	.text-muted{color: @text-muted !important;}
}
.bg-white-only{background-color:#fff;}

a.bg-light{
	&:hover{
		color: @link-color;
	}
}

.text-wariant(@brand-primary, primary);
.text-wariant(@brand-info, info);
.text-wariant(@brand-success, success);
.text-wariant(@brand-warning, warning);
.text-wariant(@brand-danger, danger);
.text-wariant(@brand-dark, dark);
.text-wariant(@brand-black, black);

.text-white {
  color: #fff;
}
.text-muted {
  color: @text-muted;
}

.glyphicon.glyphicon-one-fine-dot:before {
  content: "\25cf";
  font-size: 1.5em;
  line-height: 1rem;
}

.glyphicon-green {
  color: #64A500;
}
