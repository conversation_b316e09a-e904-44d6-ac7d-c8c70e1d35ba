{"name": "exceptionless", "private": true, "devDependencies": {"bower": "1.8.8", "grunt": "1.0.4", "grunt-angular-templates": "1.2.0", "grunt-browser-output": "1.0.3", "grunt-cache-bust": "1.7.0", "grunt-contrib-clean": "2.0.0", "grunt-contrib-compress": "1.6.0", "grunt-contrib-concat": "1.0.1", "grunt-contrib-connect": "2.1.0", "grunt-contrib-copy": "1.0.0", "grunt-contrib-cssmin": "3.0.0", "grunt-contrib-htmlmin": "3.1.0", "grunt-contrib-jshint": "2.1.0", "grunt-contrib-less": "2.0.0", "grunt-contrib-uglify": "4.0.1", "grunt-contrib-watch": "1.1.0", "grunt-dom-munger": "3.4.0", "grunt-gh-pages": "3.1.0", "grunt-html-angular-validate": "0.6.1", "grunt-karma": "3.0.2", "grunt-ng-annotate": "3.0.0", "grunt-replace": "1.0.1", "grunt-traceur": "0.5.5", "jasmine-core": "3.5.0", "jshint-stylish": "2.2.1", "karma": "4.4.1", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.1.1", "karma-junit-reporter": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-ng-html2js-preprocessor": "1.0.0", "load-grunt-config": "3.0.1", "puppeteer": "2.1.1", "rimraf": "3.0.2", "streamqueue": "1.1.2", "traceur": "0.0.111"}, "dependencies": {"gulp": "4.0.2"}}