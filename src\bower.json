{"name": "exceptionless", "main": "index.html", "ignore": ["**/.*", "node_modules", "bower_components"], "dependencies": {"angular": "1.7.9", "angular-animate": "1.7.9", "angular-bootstrap": "2.5.0", "angular-clipboard": "1.7.0", "angular-dialog-service": "5.3.0", "angular-filter": "0.5.17", "angular-gravatar": "0.4.2", "angular-hotkeys": "chieffancypants/angular-hotkeys#~1.7.0", "angular-input-match": "0.0.3", "angular-intercom": "2.1.1", "angular-loading-bar": "0.9.0", "angular-locker": "2.0.5", "angular-messages": "1.7.9", "angular-mocks": "1.7.9", "angular-payments": "*", "angular-resource": "1.7.9", "angular-sanitize": "1.7.9", "angular-stripe": "5.0.0", "angular-ui-router": "0.4.2", "angular-xeditable": "0.10.1", "angularjs-toaster": "3.0.0", "angulartics": "1.4.0", "animate.css": "3.7.2", "base64": "1.1.0", "bootstrap": "3.3.7", "bootstrap-daterangepicker": "2.1.27", "checklist-model": "1.0.0", "d3": "3.5.17", "es5-shim": "4.5.13", "es6-shim": "0.35.5", "exceptionless": "1.6.3", "font-awesome": "4.7.0", "handlebars": "4.7.3", "jquery": "3.4.1", "less.js": "3.11.1", "li": "1.2.1", "lodash": "4.17.15", "moment": "2.24.0", "mousetrap-js": "1.7.0", "ng-debounce-throttle": "B<PERSON><PERSON><PERSON>/ng-debounce-throttle", "ng-filters": "1.3.1", "objectid.js": "1.0.1", "query-string": "4.2.3", "restangular": "1.6.1", "rickshaw": "1.6.6", "satellizer": "0.15.5", "trunk8": "1.3.3", "twix": "1.3.1", "angular-translate": "2.18.2", "angular-translate-loader-static-files": "2.18.1"}, "resolutions": {"angular": "1.7.9", "angular-bootstrap": "2.5.0", "jquery": "3.4.1"}}