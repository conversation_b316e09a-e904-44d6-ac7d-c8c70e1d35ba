<div class="table-responsive">
  <table class="table table-striped table-bordered table-fixed b-t" refresh-on="OrganizationChanged UserChanged" refresh-action="vm.get(vm.currentOptions)" refresh-throttle="10000">
    <thead>
      <tr>
        <th>{{::'Email Address' | translate}}</th>
        <th>{{::'Full Name' | translate}}</th>
        <th class="action">{{::'Actions' | translate}}</th>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat="user in vm.users">
        <td>
          <span>{{::user.email_address}}</span>
          <span ng-if="user.has_admin_role" class="label label-info">{{::'AdminRole' | translate}}</span>
          <span ng-if="user.is_invite" class="label label-default">{{::'Invited' | translate}}</span>
        </td>
        <td>
          <span>{{::user.full_name}}</span>
        </td>
        <td>
          <div class="btn-group">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-fw fa-edit"></i> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" role="menu">
              <li ng-if="vm.settings.organizationId"><a ng-click="vm.resendNotification(user)"><i class="fa fa-envelope"></i> {{::'Resend Invite Email' | translate}}</a></li>
              <li ng-if="vm.settings.organizationId"><a ng-click="vm.remove(user)"><i class="fa fa-times"></i> {{user.is_invite ? 'Revoke Invite' : 'Remove User'  | translate}}</a></li>
              <li ng-if="!user.is_invite && vm.hasAdminRole(appVm.user)">
                <a ng-click="vm.updateAdminRole(user)"><i class="fa fa-user"></i> {{vm.hasAdminRole(user)? 'Add Admin Role' : 'Remove Admin Role'  | translate}}</a>
              </li>
            </ul>
          </div>
        </td>
      </tr>
      <tr ng-if="!vm.hasUsers()">
        <td colspan="2">
          <strong>{{::'No users were found.' | translate}}</strong>
        </td>
      </tr>
    </tbody>
  </table>

  <div class="table-footer" ng-if="vm.previous || vm.next">
    <div class="row">
      <div class="col-sm-8 col-xs-8 text-center" ng-if="vm.pageSummary">
        <small class="text-muted inline m-t-xs">{{vm.pageSummary}}</small>
      </div>
      <div class="col-sm-4 col-xs-4 text-right">
        <ul class="pagination pagination-sm m-t-none m-b-none">
          <li ng-show="vm.currentOptions.page && vm.currentOptions.page > 2"><a ng-click="vm.get()"><i class="fa fa-fast-backward"></i></a></li>
          <li ng-class="{'disabled': !vm.previous}"><a ng-disabled="!vm.previous" ng-click="!vm.previous || vm.previousPage()"><i class="fa fa-chevron-left"></i></a></li>
          <li ng-class="{'disabled': !vm.next}"><a ng-disabled="!vm.next" ng-click="!vm.next || vm.nextPage()"><i class="fa fa-chevron-right"></i></a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
