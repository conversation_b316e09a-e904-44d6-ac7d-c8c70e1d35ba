# Exceptionless.UI 项目分析文档

## 项目概述

**Exceptionless.UI** 是一个基于 AngularJS 1.x 构建的前端应用，用于异常监控和日志管理。

> ⚠️ **注意**: 此项目已被归档，不再维护。所有源码和问题已迁移至 https://github.com/exceptionless/Exceptionless

## 技术栈

### 前端框架
- **AngularJS**: 1.7.9
- **UI框架**: Bootstrap 3.3.7  
- **样式**: Less
- **图表**: <PERSON>shaw + D3.js

### 构建工具
- **构建工具**: Grunt 1.0.4
- **包管理器**: NPM + Bower
- **测试框架**: <PERSON>rma + Jasmine

### 主要依赖
- **jQuery**: 3.4.1
- **Lodash**: 4.17.15
- **Moment.js**: 2.24.0
- **Angular UI Router**: 0.4.2

## Node.js 版本要求

根据项目依赖分析，推荐使用：
- **Node.js 12.x** (推荐)
- **Node.js 10.x** (最低要求)

### 使用 NVM 切换版本
```bash
# 安装并使用 Node.js 12
nvm install 12
nvm use 12

# 验证版本
node --version
npm --version
```

## 开发环境设置

### 1. 安装依赖
```bash
# 进入源码目录
cd src

# 安装 NPM 依赖
npm install

# 安装 Bower 依赖
npx bower install
```

### 2. 启动开发服务器
```bash
# 启动开发服务器
npx grunt serve
```

项目将在 `http://ex-ui.localtest.me:5100` 启动

## 项目结构

```
src/
├── app/                    # 应用主要代码
│   ├── account/           # 账户管理
│   ├── admin/             # 管理后台
│   ├── auth/              # 认证相关
│   ├── event/             # 事件处理
│   ├── organization/      # 组织管理
│   ├── project/           # 项目管理
│   └── ...
├── components/            # 可复用组件
├── less/                  # 样式文件
├── grunt/                 # Grunt 配置
├── package.json          # NPM 依赖
├── bower.json            # Bower 依赖
└── Gruntfile.js          # Grunt 主配置
```

## API 配置

### 主要配置文件: `src/app.config.js`

```javascript
angular.module('app.config', [])
  .constant('BASE_URL', 'https://localhost:5001')           // API 基础地址
  .constant('EXCEPTIONLESS_API_KEY')                        // API 密钥
  .constant('EXCEPTIONLESS_SERVER_URL')                     // 服务器地址
  .constant('FACEBOOK_APPID')                               // Facebook 应用ID
  .constant('GITHUB_APPID')                                 // GitHub 应用ID
  .constant('GOOGLE_APPID')                                 // Google 应用ID
  // ... 其他配置
```

### API 地址设置位置

- **文件**: `src/app.config.js:5`
- **默认值**: `https://localhost:5001`
- **实际API路径**: `BASE_URL + '/api/v2'` (在 `app.js:115` 中配置)

### 修改API地址

```javascript
// 修改 src/app.config.js 文件中的 BASE_URL
.constant('BASE_URL', 'https://your-api-server.com')
```

## 构建发布

### 构建命令

```bash
# 进入源码目录
cd src

# 构建生产版本
npx grunt build

# 或者直接运行 (build 是默认任务)
npx grunt
```

### 构建流程 (src/Gruntfile.js:49)

```javascript
grunt.registerTask('build', [
  'jshint',        // JS 代码检查
  'clean:before',  // 清理构建目录
  'less',          // 编译 Less 样式
  'dom_munger',    // HTML 处理
  'ngtemplates',   // Angular 模板缓存
  'cssmin',        // CSS 压缩
  'concat',        // 文件合并
  'ngAnnotate',    // Angular 依赖注入注解
  'uglify',        // JS 压缩混淆
  'copy',          // 复制资源文件
  'htmlmin',       // HTML 压缩
  'replace',       // 内容替换
  'cacheBust',     // 缓存破坏
  'clean:after'    // 清理临时文件
]);
```

### 其他可用任务

```bash
# 开发服务器
npx grunt serve

# 运行测试
npx grunt test

# 创建 ZIP 包
npx grunt zip

# 发布到 GitHub Pages
npx grunt publish
```

### 构建输出

- **输出目录**: `dist/`
- **主要文件**:
  - `index.html` - 主页面
  - `app.min.js` - 压缩后的JS文件
  - `app.min.css` - 压缩后的CSS文件
  - `img/` - 图片资源
  - `lang/` - 语言文件

### ZIP 打包

构建完成后可以创建部署包：

```bash
# 设置版本号环境变量 (可选)
export APPVEYOR_BUILD_VERSION=1.0.0

# 创建 ZIP 包
npx grunt zip
```

生成的文件: `Exceptionless.UI.{version}.zip`

## 注意事项

1. **技术栈较老**: 使用 AngularJS 1.x + Grunt，建议迁移到现代框架
2. **项目已归档**: 不再维护，仅供参考学习
3. **兼容性**: 确保使用推荐的 Node.js 版本避免依赖问题
4. **安全性**: 旧版依赖可能存在安全漏洞，生产环境需谨慎使用