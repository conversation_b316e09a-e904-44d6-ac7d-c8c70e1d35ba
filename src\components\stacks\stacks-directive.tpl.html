<div class="table-responsive" refresh-on="filterChanged" refresh-action="vm.get()">
  <table class="table table-striped table-bordered table-selectable table-fixed b-t table-hover table-clickable" refresh-on="StackChanged PlanChanged" refresh-if="vm.canRefresh(data)" refresh-action="vm.get(vm.currentOptions)" refresh-throttle="10000">
    <thead>
    <tr>
      <th class="selection hidden-xs">
        <label class="checks m-b-none">
          <input type="checkbox" ng-click="vm.updateSelection()" ng-checked="vm.selectedIds.length > 0" ng-disabled="vm.stacks.length === 0"><i></i>
        </label>
      </th>
      <th>{{::'Summary' | translate}}</th>
      <th class="percentage">{{::'UserRatio' | translate}}</th>
      <th class="number">{{::'Events' | translate}}</th>
      <th class="date hidden-xs">{{::'First' | translate}}</th>
      <th class="date hidden-xs">{{::'Last' | translate}}</th>
    </tr>
    </thead>
    <tbody>
    <tr class="row-clickable" ng-repeat="stack in vm.stacks track by stack.id" ng-if="vm.stacks.length > 0">
      <td class="hidden-xs">
        <label class="checks m-b-none"><input type="checkbox" checklist-model="vm.selectedIds" checklist-value="stack.id"><i></i></label>
      </td>
      <td ng-click="vm.open(stack.id, $event)">
        <summary source="stack" show-type="vm.showType" show-status="vm.showStatus"></summary>
      </td>
      <td ng-click="vm.open(stack.id, $event)" class="number">
        <abbr title="{{stack.users | number:0}} of {{stack.total_users | number:0}} users">{{(stack.total_users > 0 ? stack.users / stack.total_users * 100.0 : 0) | percentage:100}}</abbr>
      </td>
      <td ng-click="vm.open(stack.id, $event)" class="number">{{stack.total | number:0}}</td>
      <td ng-click="vm.open(stack.id, $event)" class="hidden-xs">
        <abbr title="{{::'DateTime' | translate : {'date' : stack.first_occurrence} }}">
          <timeago date="stack.first_occurrence"></timeago>
        </abbr>
      </td>
      <td ng-click="vm.open(stack.id, $event)" class="hidden-xs"><abbr title="{{::'DateTime' | translate : {'date' : stack.last_occurrence} }}">
        <timeago date="stack.last_occurrence"></timeago>
      </abbr></td>
    </tr>
    <tr ng-if="vm.stacks.length === 0 || vm.loading">
      <td class="hidden-xs"><label class="checks m-b-none"><input type="checkbox" disabled><i></i></label></td>
      <td colspan="5" class="hidden-xs">
        <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
        <strong ng-if="!vm.loading">{{vm.hasFilter() ? 'No stacks were found with the current filter.': 'No stacks were found.' | translate}}</strong>
      </td>
      <td colspan="3" class="visible-xs">
        <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
        <strong ng-if="!vm.loading">{{vm.hasFilter() ? 'No stacks were found with the current filter.': 'No stacks were found.' | translate}}</strong>
      </td>
    </tr>
    </tbody>
  </table>

  <div class="table-footer">
    <div class=" row">
      <div class="col-sm-4 hidden-xs">
        <div class="dropdown">
          <button type="button" role="button" id="bulkActions" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
            {{::'Bulk Action' | translate}}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu" aria-labelledby="bulkActions">
            <li role="presentation"><a role="menuitem" tabindex="-1" ng-repeat="action in vm.actions" ng-click="vm.save(action)">{{::action.name | translate}}</a></li>
          </ul>
        </div>
      </div>
      <div class="col-sm-4 text-center" ng-class="vm.previous || vm.next ? 'col-xs-8': 'col-xs-12'" ng-if="vm.pageSummary">
        <small class="text-muted inline m-t-xs">{{vm.pageSummary}}</small>
      </div>
      <div class="col-sm-4 col-xs-4 text-right" ng-if="vm.previous || vm.next">
        <ul class="pagination pagination-sm m-t-none m-b-none">
          <li ng-show="vm.currentOptions.page && vm.currentOptions.page > 2"><a ng-click="vm.get()"><i class="fa fa-fast-backward"></i></a></li>
          <li ng-class="{'disabled': !vm.previous}"><a ng-disabled="!vm.previous" ng-click="!vm.previous || vm.previousPage()"><i class="fa fa-chevron-left"></i></a></li>
          <li ng-class="{'disabled': !vm.next}"><a ng-disabled="!vm.next" ng-click="!vm.next || vm.nextPage()"><i class="fa fa-chevron-right"></i></a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
