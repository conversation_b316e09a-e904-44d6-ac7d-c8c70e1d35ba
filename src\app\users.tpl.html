<organization-notifications></organization-notifications>

<div class="hbox hbox-auto-xs hbox-auto-sm" refresh-on="PersistentEventChanged PlanChanged" refresh-action="vm.get()" refresh-if="vm.canRefresh(data)" refresh-throttle="10000">
  <div class="col" refresh-on="filterChanged" refresh-action="vm.get()">
    <div class="wrapper-md">
      <div class="row">
        <div class="col-sm-12">
          <div class="row row-sm text-center">
            <div class="col-md-3 col-sm-6 col-xs-6">
              <a ng-href="{{appVm.urls.events[vm.type]}}" class="dashboard-block">
                <div class="rotate">
                  <i class="fa fa-calendar fa-4x"></i>
                </div>
                <div class="details">
                  <span class="title">{{::'Events' | translate}}</span>
                  <span class="sub">{{vm.stats.events}}</span>
                </div>
                <i class="fa fa-chevron-right fa-2x more"></i>
              </a>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-6">
              <a ng-href="{{appVm.urls.frequent[vm.type]}}" class="dashboard-block">
                <div class="rotate">
                  <i class="fa fa-key fa-4x"></i>
                </div>
                <div class="details">
                  <span class="title">{{::'Stacks' | translate}}</span>
                  <span class="sub">{{vm.stats.stacks}}</span>
                </div>
                <i class="fa fa-chevron-right fa-2x more"></i>
              </a>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-6">
              <a ng-href="{{appVm.urls.new[vm.type]}}" class="dashboard-block">
                <div class="rotate">
                  <i class="fa fa-asterisk fa-4x"></i>
                </div>
                <div class="details">
                  <span class="title">{{::'New Stacks' | translate}}</span>
                  <span class="sub">{{vm.stats.newStacks}}</span>
                </div>
                <i class="fa fa-chevron-right fa-2x more"></i>
              </a>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-6">
              <div class="dashboard-block">
                <div class="rotate">
                  <i class="fa fa-line-chart fa-4x"></i>
                </div>
                <div class="details">
                  <span class="title">{{::'Events Per Hour' | translate}}</span>
                  <span class="sub">{{vm.stats.avg_per_hour}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-bar-chart-o"></i>{{::'History' | translate}}</div>
        <div class="panel-body">
          <rickshaw options="vm.chart.options" features="vm.chart.features"></rickshaw>
        </div>
      </div>

      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-users"></i> {{::'Most Users Stacks' | translate}}</div>
        <stacks settings="vm.mostUsers"></stacks>
      </div>
    </div>
  </div>
</div>
