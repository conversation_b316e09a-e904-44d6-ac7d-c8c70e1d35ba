<div class="hbox hbox-auto-xs hbox-auto-sm">
  <div class="col" refresh-on="UserChanged ProjectChanged" refresh-action="vm.get(data)" refresh-debounce="1000">
    <div class="wrapper-md">
      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-user"></i> {{::'My Account' | translate}}</div>
        <div class="panel-body m-b-n">
          <uib-tabset class="tab-container" active="vm.activeTabIndex">
            <uib-tab heading="{{::'General' | translate}}">
              <form name="fullNameForm" role="form" class="form-validation" autocomplete="on">
                <div class="form-group">
                  <img gravatar-src="vm.user.email_address" gravatar-size="100" alt="{{vm.user.full_name}}" class="img-thumbnail">
                  <div>
                    <small ng-bind-html="'NOTICE_GRAVATAR' | translate">
                    </small>
                  </div>
                </div>

                <div class="form-group">
                  <label for="name">{{::'Full Name' | translate}}</label>
                  <input id="name" name="name" type="text" class="form-control" x-autocompletetype="full-name" autocapitalize="words" autocorrect="off" spellcheck="false"
                         placeholder="{{::'Your first and last name' | translate}}"
                         ng-model="vm.user.full_name"
                         ng-model-options="{ debounce: 500 }"
                         ng-change="vm.saveUser(fullNameForm.$valid)"
                         ng-required="true" />

                  <div class="error" ng-messages="fullNameForm.name.$error" ng-if="fullNameForm.$submitted || fullNameForm.name.$touched">
                    <small ng-message="required">{{::'Full Name is required.' | translate}}</small>
                  </div>
                </div>
              </form>

              <form name="vm.emailAddressForm" role="form" class="form-validation" autocomplete="on">
                <div class="form-group">
                  <label for="email">{{::'Email Address' | translate}}</label>
                  <div ng-class="{'input-group': !!vm.emailAddressForm.$pending }">
                    <input id="email" name="email" type="email" class="form-control" x-autocompletetype="email" autocorrect="off" spellcheck="false"
                           placeholder="{{::'Email Address' | translate}}"
                           ng-model="vm.user.email_address"
                           ng-model-options="{ debounce: 1000 }"
                           ng-change="vm.saveEmailAddress()"
                           email-address-available-validator
                           required/>

                    <span class="input-group-addon" ng-if="vm.emailAddressForm.$pending">
                      <i class="fa fa-fw fa-spinner fa-spin"></i>
                    </span>
                  </div>

                  <div class="error" ng-messages="vm.emailAddressForm.email.$error" ng-if="vm.emailAddressForm.$submitted || vm.emailAddressForm.email.$touched">
                    <small ng-message="required">{{::'Email Address is required.' | translate}}</small>
                    <small ng-message="email">{{::'Email Address is required.' | translate}}</small>
                    <small ng-message="unique">{{::'A user already exists with this email address.' | translate}}</small>
                  </div>

                  <p ng-if="!vm.user.is_email_address_verified" class="help-block">
                      {{::'Email not verified.' | translate}} <a ng-click="vm.resendVerificationEmail()">{{::'Resend' | translate}}</a> {{::'verification email.' | translate}}
                  </p>
                </div>
              </form>
            </uib-tab>
            <uib-tab heading="{{::'Notifications' | translate}}">
              <form role="form" class="form-validation">
                <div class="alert in fade alert-danger" ng-if="!vm.user.is_email_address_verified || !vm.user.email_notifications_enabled">
                    {{::'Email notifications are currently disabled.' | translate}} <span ng-if="!vm.user.is_email_address_verified">{{::'To enable email notifications you must first verify your email address.' | translate}} <a ng-click="vm.resendVerificationEmail()">{{::'Resend' | translate}}</a> {{::'verification email.' | translate}}</span>
                </div>
                <div class="checkbox">
                  <label class="checks">
                    <input type="checkbox"
                           ng-model="vm.user.email_notifications_enabled"
                           ng-model-options="{ debounce: 500 }"
                           ng-change="vm.saveEnableEmailNotification()">
                    <i></i>
                    {{::'Enable email notifications' | translate}}
                  </label>
                </div>

                <div ng-if="vm.projects.length > 0">
                  <hr/>

                  <p>{{::'Choose how often you want to receive notifications for event occurrences in this project.' | translate}}</p>

                  <select class="form-control"
                          ng-model="vm.currentProject"
                          ng-change="vm.getEmailNotificationSettings()"
                          ng-disabled="!(vm.user.email_notifications_enabled && vm.emailNotificationSettings)"
                          ng-options="project.name group by project.organization_name for project in vm.projects | orderBy: 'name' track by project.id"></select>

                  <div class="checkbox">
                    <label class="checks">
                      <input type="checkbox"
                             ng-model="vm.emailNotificationSettings.send_daily_summary"
                             ng-model-options="{ debounce: 500 }"
                             ng-change="vm.saveEmailNotificationSettings()"
                             ng-disabled="!(vm.user.email_notifications_enabled && vm.emailNotificationSettings)">
                      <i></i>
                      {{::'Send daily project summary' | translate}}
                    </label>
                  </div>

                  <hr ng-if="!vm.hasPremiumFeatures"/>
                  <div class="alert in fade alert-success" ng-if="!vm.hasPremiumFeatures">
                    <a ng-click="vm.showChangePlanDialog()">{{::'Upgrade now' | translate}}</a> {{::'to enable occurrence level notifications!' | translate}}
                  </div>

                  <div class="checkbox">
                    <label class="checks">
                      <input type="checkbox"
                             ng-model="vm.emailNotificationSettings.report_new_errors"
                             ng-model-options="{ debounce: 500 }"
                             ng-change="vm.saveEmailNotificationSettings()"
                             ng-disabled="!vm.hasPremiumEmailNotifications()">
                      <i></i>
                      {{::'Notify me on new errors' | translate}}
                    </label>
                  </div>

                  <div class="checkbox">
                    <label class="checks">
                      <input type="checkbox"
                             ng-model="vm.emailNotificationSettings.report_critical_errors"
                             ng-model-options="{ debounce: 500 }"
                             ng-change="vm.saveEmailNotificationSettings()"
                             ng-disabled="!vm.hasPremiumEmailNotifications()">
                      <i></i>
                      {{::'Notify me on critical errors' | translate}}
                    </label>
                  </div>

                  <div class="checkbox">
                    <label class="checks">
                      <input type="checkbox"
                             ng-model="vm.emailNotificationSettings.report_event_regressions"
                             ng-model-options="{ debounce: 500 }"
                             ng-change="vm.saveEmailNotificationSettings()"
                             ng-disabled="!vm.hasPremiumEmailNotifications()">
                      <i></i>
                      {{::'Notify me on error regressions' | translate}}
                    </label>
                  </div>

                  <div class="checkbox">
                    <label class="checks">
                      <input type="checkbox"
                             ng-model="vm.emailNotificationSettings.report_new_events"
                             ng-model-options="{ debounce: 500 }"
                             ng-change="vm.saveEmailNotificationSettings()"
                             ng-disabled="!vm.hasPremiumEmailNotifications()">
                      <i></i>
                      {{::'Notify me on new events' | translate}}
                    </label>
                  </div>

                  <div class="checkbox">
                    <label class="checks">
                      <input type="checkbox"
                             ng-model="vm.emailNotificationSettings.report_critical_events"
                             ng-model-options="{ debounce: 500 }"
                             ng-change="vm.saveEmailNotificationSettings()"
                             ng-disabled="!vm.hasPremiumEmailNotifications()">
                      <i></i>
                      {{::'Notify me on critical events' | translate}}
                    </label>
                  </div>
                </div>
              </form>
            </uib-tab>
            <uib-tab heading="{{::'Password' | translate}}">
              <form name="vm.passwordForm" role="form" class="form-validation">
                <div class="form-group" ng-if="vm.hasLocalAccount">
                  <label for="current">{{::"Current Password" | translate}}</label>
                  <input id="current" name="current" type="password" class="form-control" ng-model="vm.password.current_password" required />

                  <div class="error" ng-messages="vm.passwordForm.current.$error" ng-if="vm.passwordForm.$submitted || vm.passwordForm.current.$touched">
                    <small ng-message="required">{{::"Current Password is required." | translate}}</small>
                  </div>
                </div>

                <div class="form-group">
                  <label for="newPassword">{{::'New Password' | translate}}</label>
                  <input id="newPassword" name="newPassword" type="password" class="form-control" ng-model="vm.password.password" ng-minlength="6" ng-maxlength="100" required />

                  <div class="error" ng-messages="vm.passwordForm.newPassword.$error" ng-if="vm.passwordForm.$submitted || vm.passwordForm.newPassword.$touched">
                    <small ng-message="required">{{::'New Password is required.' | translate}}</small>
                    <small ng-message="minlength">{{::'New Password must be at least 6 characters long.' | translate}}</small>
                    <small ng-message="maxlength">{{::'New Password must be less than 101 characters long.' | translate}}</small>
                  </div>
                </div>

                <div class="form-group">
                  <label for="confirmPassword">{{::'Confirm password' | translate}}</label>
                  <input id="confirmPassword" name="confirmPassword" type="password" class="form-control"
                         ng-model="vm.password.confirm_password"
                         match="vm.password.password"
                         ng-minlength="6"
                         ng-maxlength="100"
                         ng-required="true" />

                  <div class="error" ng-messages="vm.passwordForm.confirmPassword.$error" ng-if="vm.passwordForm.$submitted || vm.passwordForm.confirmPassword.$touched">
                    <small ng-message="match">{{::'New Password and Confirmation Password fields do not match.' | translate}}</small>
                    <small ng-message="required">{{::'Confirm Password is required.' | translate}}</small>
                    <small ng-message="minlength">{{::'Confirm Password must be at least 6 characters long.' | translate}}</small>
                    <small ng-message="maxlength">{{::'Confirm Password must be less than 101 characters long.' | translate}}</small>
                  </div>
                </div>

                <button type="submit" role="button" class="btn btn-primary" promise-button="vm.changePassword(vm.passwordForm.$valid)" promise-button-busy-text="{{vm.hasLocalAccount ? 'Changing Password' : 'Setting Password' | translate}}">{{vm.hasLocalAccount ? 'Change Password' : 'Set Password' | translate}}</button>
              </form>
            </uib-tab>
            <uib-tab heading="{{::'External Logins' | translate}}" ng-if="vm.isExternalLoginEnabled()">
              <h4>{{::'Add an external login' | translate}}</h4>

              <div>
                <button type="button" role="button" ng-click="vm.authenticate('live')" ng-if="vm.isExternalLoginEnabled('live')" class="btn btn-large image-button icon-login-microsoft" title="{{::'Log in using your Microsoft account' | translate}}"></button>
                <button type="button" role="button" ng-click="vm.authenticate('google')" ng-if="vm.isExternalLoginEnabled('google')" class="btn btn-large image-button icon-login-google" title="{{::'Log in using your Google account' | translate}}"></button>
                <button type="button" role="button" ng-click="vm.authenticate('facebook')" ng-if="vm.isExternalLoginEnabled('facebook')" class="btn btn-large image-button icon-login-facebook" title="{{::'Log in using your Facebook account' | translate}}"></button>
                <button type="button" role="button" ng-click="vm.authenticate('github')" ng-if="vm.isExternalLoginEnabled('github')" class="btn btn-large image-button icon-login-github" title="{{::'Log in using your GitHub account' | translate}}"></button>
              </div>

              <h4>{{::'Existing external logins' | translate}}</h4>
              <div class="table-responsive">
                <table class="table table-striped table-bordered table-fixed b-t">
                  <thead>
                    <tr>
                      <th>{{::'Name' | translate}}</th>
                      <th class="action">{{::'Actions' | translate}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="account in vm.user.o_auth_accounts" ng-if="vm.user.o_auth_accounts.length > 0">
                      <td>{{::account.provider}} ({{::account.username || account.provider_user_id}})</td>
                      <td>
                        <button type="button" role="button" class="btn btn-sm" title="{{::'Remove' | translate}}"
                                ng-disabled="!(vm.hasLocalAccount || vm.user.o_auth_accounts.length > 1)"
                                ng-click="vm.unlink(account)">
                          <i class="fa fa-times"></i>
                        </button>
                      </td>
                    </tr>
                    <tr ng-if="vm.user.o_auth_accounts.length === 0">
                      <td colspan="2">
                        <strong>{{::'No external logins were found.' | translate}}</strong>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </uib-tab>
          </uib-tabset>
        </div>
        <footer class="panel-footer">
          <div class="pull-right">
            <div ng-if="!vm.currentProject.id">
              <a ui-sref="app.frequent" class="btn btn-default" role="button">{{::'Go To Most Frequent' | translate}}</a>
            </div>
            <div ng-if="vm.currentProject.id">
              <a ui-sref="app.project-frequent({ projectId: vm.currentProject.id })" class="btn btn-default" role="button">{{::'Go To Most Frequent' | translate}}</a>
            </div>
          </div>
          <div>
            <a ng-click="vm.deleteAccount()" class="btn btn-danger" role="button">{{::'DELETE ACCOUNT' | translate}}</a>
          </div>
          <div class="clearfix"></div>
        </footer>
      </div>
    </div>
  </div>
</div>
