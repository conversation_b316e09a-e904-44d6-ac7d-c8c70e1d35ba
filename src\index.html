<!DOCTYPE html>
<html ng-app="app">
    <head>
        <title ng-bind="page.title">Exceptionless</title>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimal-ui"/>
        <meta name="description" content="Exceptionless provides real-time error reporting for your apps. It organizes the gathered information into simple actionable data that will help your app become exceptionless!"/>

        <meta property="og:type" content="website" />
        <meta property="og:title" content="Exceptionless" />
        <meta property="og:description" content="Exceptionless provides real-time error reporting for your apps. It organizes the gathered information into simple actionable data that will help your app become exceptionless!" />
        <meta property="og:image" content="//img/exceptionless.png" />
        <meta name="application-name" content="Exceptionless"/>
        <meta name="fragment" content="!" />
        <meta name="msapplication-TileColor" content="#2c2c2c"/>
        <meta name="msapplication-TileImage" content="/img/exceptionless-144.png"/>
        <meta name="msapplication-navbutton-color" content="#73C03A" />
        <meta name="msapplication-starturl" content="/project"/>
        <meta name="msapplication-task" content="name=Most Frequent; action-uri=/frequent; icon-uri=/img/icons/frequent.ico" />
        <meta name="msapplication-task" content="name=New Events; action-uri=/new; icon-uri=/img/icons/new.ico" />
        <meta name="msapplication-task" content="name=Events; action-uri=/; icon-uri=/img/icons/events.ico" />

        <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
        <link rel="dns-prefetch" href="https://fonts.googleapis.com">
        <link rel="dns-prefetch" href="https://widget.intercom.io">
        <link rel="dns-prefetch" href="https://www.googletagmanager.com">
        <link rel="dns-prefetch" href="https://connect.facebook.net">

        <link rel="prefetch" href="lang/en-us.json">
        <link rel="prefetch" href="lang/zh-cn.json">

        <link rel='icon' href='/favicon.ico' type="image/x-icon" data-remove="false" />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" data-remove="false" />

        <meta name="apple-mobile-web-app-title" content="Exceptionless" />
        <link rel="apple-touch-icon" sizes="57x57" href="/touch-icon-ipad-114.png" />
        <link rel="apple-touch-icon" sizes="72x72" href="/touch-icon-ipad-114.png" />
        <link rel="apple-touch-icon" sizes="114x114" href="/touch-icon-ipad-114.png" />
        <link rel="apple-touch-icon" sizes="144x144" href="/touch-icon-ipad-144.png" />

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,700" />

        <link href="app.less" type="text/css" rel="stylesheet/less" data-concat="true" data-remove="true" />
    </head>
    <body loading-bar>
        <google-tag-manager></google-tag-manager>
        <div class="app" id="app" ui-view autoscroll="true"></div>
        <toaster-container toaster-options="{'position-class': 'toast-bottom-right'}"></toaster-container>

        <!--[if lte IE 9]><script src="https://cdn.jsdelivr.net/npm/Base64@1.0.2/base64.min.js" data-concat="false" data-remove="false"></script><![endif]-->
        <script src="https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.min.js" data-concat="false" data-remove="false"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/js/bootstrap.min.js" data-concat="false" data-remove="false"></script>
        <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js" data-concat="false" data-remove="false"></script>

        <!-- Livereload script for development only (stripped during dist build) -->
        <script src="http://localhost:35729/livereload.js" data-concat="false"></script>

        <!-- JS from CDN (temporary replacement for bower components) -->
        <script src="https://cdn.jsdelivr.net/npm/es5-shim@4.5.13/es5-shim.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/es6-shim@0.35.5/es6-shim.min.js"></script>
        <!-- <script src="bower_components/exceptionless/dist/exceptionless.js"></script> -->
        <script type="application/javascript" data-concat="false" data-remove="true">
          // exceptionless.ExceptionlessClient.default.config.useDebugLogger();
        </script>
        <script src="https://cdn.jsdelivr.net/npm/less@3.11.1/dist/less.min.js" data-concat="false"></script>
        <script src="https://cdn.jsdelivr.net/npm/moment@2.24.0/moment.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/moment@2.24.0/locale/zh-cn.js"></script>
        <!-- <script src="bower_components/bootstrap-daterangepicker/daterangepicker.js"></script> -->
        <!-- <script src="bower_components/twix/dist/twix.js"></script> -->
        <!-- <script src="bower_components/objectid.js/src/main/javascript/Objectid.js"></script> -->
        <script src="https://cdn.jsdelivr.net/npm/handlebars@4.7.3/dist/handlebars.min.js"></script>
        <!-- <script src="bower_components/trunk8/trunk8.js"></script> -->

        <script src="https://cdn.jsdelivr.net/npm/angular@1.7.9/angular.min.js"></script>
        <!-- <script src="bower_components/exceptionless/dist/integrations/angular.js"></script> -->
        <script src="https://cdn.jsdelivr.net/npm/angular-animate@1.7.9/angular-animate.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/angular-ui-bootstrap@2.5.0/dist/ui-bootstrap-tpls.min.js"></script>
        <!-- <script src="bower_components/angular-clipboard/angular-clipboard.js"></script> -->
        <!-- <script src="bower_components/angular-dialog-service/dist/dialogs.js"></script> -->
        <!-- <script src="bower_components/angular-filter/dist/angular-filter.js"></script> -->
        <!-- <script src="bower_components/angular-gravatar/build/angular-gravatar.js"></script> -->
        <!-- <script src="bower_components/angular-hotkeys/src/hotkeys.js"></script> -->
        <!-- <script src="bower_components/angular-input-match/dist/angular-input-match.js"></script> -->
        <!-- <script src="bower_components/angular-intercom/angular-intercom.js"></script> -->
        <!-- <script src="bower_components/angular-loading-bar/src/loading-bar.js"></script> -->
        <!-- <script src="bower_components/angular-locker/src/angular-locker.js"></script> -->
        <script src="https://cdn.jsdelivr.net/npm/angular-messages@1.7.9/angular-messages.min.js"></script>
        <!-- <script src="bower_components/angular-payments/lib/angular-payments.js"></script> -->
        <script src="https://cdn.jsdelivr.net/npm/angular-resource@1.7.9/angular-resource.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/angular-sanitize@1.7.9/angular-sanitize.min.js"></script>
        <!-- <script src="bower_components/angular-stripe/release/angular-stripe.js"></script> -->
        <script src="https://cdn.jsdelivr.net/npm/angular-translate@2.18.2/dist/angular-translate.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/angular-translate-loader-static-files@2.18.1/angular-translate-loader-static-files.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/@uirouter/angularjs@1.0.30/release/angular-ui-router.min.js"></script>
        <!-- <script src="bower_components/angular-xeditable/dist/js/xeditable.js"></script> -->
        <!-- <script src="bower_components/angularjs-toaster/toaster.js"></script> -->
        <!-- <script src="bower_components/angulartics/src/angulartics.js"></script> -->
        <!-- <script src="bower_components/angulartics/src/angulartics-gtm.js"></script> -->
        <!-- <script src="bower_components/checklist-model/checklist-model.js"></script> -->
        <!-- <script src="bower_components/mousetrap-js/mousetrap.js"></script> -->
        <!-- <script src="bower_components/ng-debounce/angular-debounce.js"></script> -->
        <!-- <script src="bower_components/ng-filters/dist/ng-filters.js"></script> -->
        <!-- <script src="bower_components/restangular/dist/restangular.js"></script> -->
        <!-- <script src="components/satellizer/satellizer.js"></script> -->

        <script src="https://cdn.jsdelivr.net/npm/d3@3.5.17/d3.min.js"></script>
        <!-- <script src="bower_components/rickshaw/rickshaw.js"></script> -->

        <!-- <script src="bower_components/li/lib/index.js"></script> -->
        <!-- Add New Bower Component JS Above -->

        <!-- Main App JS -->
        <script src="app.js"></script>
        <script src="app.config.js" data-concat="false"></script>

        <script src="components/admin/admin-service.js"></script>
        <script src="components/analytics/analytics-service.js"></script>
        <script src="components/auth/auth-service.js"></script>
        <script src="components/auto-active/auto-active-directive.js"></script>
        <script src="components/autofocus/autofocus-directive.js"></script>
        <script src="components/billing/billing.js"></script>
        <script src="components/billing/billing-service.js"></script>
        <script src="components/billing/change-plan-controller.js"></script>
        <script src="components/date-filter/date-filter.js"></script>
        <script src="components/date-filter/custom-date-range-controller.js"></script>
        <script src="components/date-filter/date-filter-directive.js"></script>
        <script src="components/date-range-parser/date-range-parser-service.js"></script>
        <script src="components/date-range-picker/date-range-picker-directive.js"></script>
        <script src="components/dialog/dialog.js"></script>
        <script src="components/dialog/dialog-service.js"></script>
        <script src="components/dialog/confirm-dialog-controller.js"></script>
        <script src="components/dialog/json-dialog-controller.js"></script>
        <script src="components/duration/duration-directive.js"></script>

        <script src="components/error/error-service.js"></script>
        <script src="components/event/event-service.js"></script>
        <script src="components/events/events.js"></script>
        <script src="components/events/events-actions-service.js"></script>
        <script src="components/events/events-directive.js"></script>
        <script src="components/filter/filter.js"></script>
        <script src="components/filter/filter-service.js"></script>
        <script src="components/filter/filter-storage-service.js"></script>
        <script src="components/intercom/intercom.js"></script>
        <script src="components/intercom/intercom-directive.js"></script>
        <script src="components/link/link-service.js"></script>
        <script src="components/loading-bar/loading-bar-directive.js"></script>
        <script src="components/log-level/log-level-directive.js"></script>
        <script src="components/notification/notification-service.js"></script>
        <script src="components/object-dump/object-dump.js"></script>
        <script src="components/object-dump/object-dump-directive.js"></script>
        <script src="components/object-dump/handlebars-service.js"></script>
        <script src="components/objectid/objectid-service.js"></script>
        <script src="components/organization/organization-service.js"></script>
        <script src="components/organization-notifications/organization-notifications-directive.js"></script>
        <script src="components/pagination/pagination-service.js"></script>
        <script src="components/project/project.js"></script>
        <script src="components/project/project-service.js"></script>
        <script src="components/project-filter/project-filter-directive.js"></script>
        <script src="components/projects/projects-directive.js"></script>
        <script src="components/promise-button/promise-button-directive.js"></script>
        <script src="components/rate-limit/rate-limit.js"></script>
        <script src="components/rate-limit/rate-limit-directive.js"></script>
        <script src="components/rate-limit/rate-limit-service.js"></script>
        <script src="components/refresh-on/refresh-on-directive.js"></script>
        <script src="components/relative-time/relative-time-directive.js"></script>
        <script src="components/release-notification/release-notification-directive.js"></script>
        <script src="components/rickshaw/rickshaw-directive.js"></script>
        <script src="components/rickshaw/rickshaw-custom.js"></script>
        <script src="components/search/search-service.js"></script>
        <script src="components/search-filter/search-filter.js"></script>
        <script src="components/search-filter/search-filter-directive.js"></script>
        <script src="components/semver/semver-directive.js"></script>
        <script src="components/show-on-hover-parent/show-on-hover-parent-directive.js"></script>
        <script src="components/simple-error/simple-error-service.js"></script>
        <script src="components/simple-stack-trace/simple-stack-trace-directive.js"></script>
        <script src="components/stack/stack-service.js"></script>
        <script src="components/stack-dialog/stack-dialog.js"></script>
        <script src="components/stack-dialog/stack-dialog-service.js"></script>
        <script src="components/stack-dialog/mark-fixed-dialog-controller.js"></script>
        <script src="components/stack-trace/stack-trace-directive.js"></script>
        <script src="components/stacks/stacks.js"></script>
        <script src="components/stacks/stacks-actions-service.js"></script>
        <script src="components/stacks/stacks-directive.js"></script>
        <script src="components/state/state-service.js"></script>
        <script src="components/status/status-service.js"></script>
        <script src="components/system-notification/system-notification-directive.js"></script>
        <script src="components/summary/summary-directive.js"></script>
        <script src="components/timeago/timeago-directive.js"></script>
        <script src="components/token/token-service.js"></script>
        <script src="components/truncate/truncate-directive.js"></script>
        <script src="components/user/user-service.js"></script>
        <script src="components/users/users-directive.js"></script>
        <script src="components/url/url-service.js"></script>
        <script src="components/ui-nav/ui-nav-directive.js"></script>
        <script src="components/ui-scroll/ui-scroll-directive.js"></script>
        <script src="components/ui-shift/ui-shift-directive.js"></script>
        <script src="components/ui-toggle-class/ui-toggle-class-directive.js"></script>
        <script src="components/validators/validators.js"></script>
        <script src="components/validators/email-addresss-available-validator.js"></script>
        <script src="components/validators/organization-name-available-validator.js"></script>
        <script src="components/validators/project-name-available-validator.js"></script>
        <script src="components/validators/search-filter-validator.js"></script>
        <script src="components/validators/semantic-version-validator.js"></script>
        <script src="components/web-hook/web-hook.js"></script>
        <script src="components/web-hook/add-web-hook-dialog-controller.js"></script>
        <script src="components/web-hook/web-hook-service.js"></script>
        <script src="components/translate/translate-service.js"></script>
        <script src="components/websocket/websocket-service.js"></script>

        <script src="app/app-controller.js"></script>
        <script src="app/events-controller.js"></script>
        <script src="app/frequent-controller.js"></script>
        <script src="app/new-controller.js"></script>
        <script src="app/users-controller.js"></script>

        <script src="app/auth/auth.js"></script>
        <script src="app/auth/forgot-password-controller.js"></script>
        <script src="app/auth/login-controller.js"></script>
        <script src="app/auth/logout-controller.js"></script>
        <script src="app/auth/reset-password-controller.js"></script>
        <script src="app/auth/signup-controller.js"></script>

        <script src="app/account/account.js"></script>
        <script src="app/account/manage-controller.js"></script>
        <script src="app/account/verify-controller.js"></script>

        <script src="app/admin/admin.js"></script>
        <script src="app/admin/dashboard-controller.js"></script>

        <script src="app/event/event.js"></script>
        <script src="app/event/event-controller.js"></script>
        <script src="app/event/extended-data-item-directive.js"></script>
        <script src="app/event/reference-controller.js"></script>

        <script src="app/organization/organization.js"></script>
        <script src="app/organization/list/add-organization-dialog-controller.js"></script>
        <script src="app/organization/list/list-controller.js"></script>
        <script src="app/organization/manage/add-user-dialog-controller.js"></script>
        <script src="app/organization/manage/invoices-directive.js"></script>
        <script src="app/organization/manage/manage-controller.js"></script>
        <script src="app/organization/upgrade/upgrade-controller.js"></script>

        <script src="app/payment/payment.js"></script>
        <script src="app/payment/payment-controller.js"></script>

        <script src="app/project/project.js"></script>
        <script src="app/project/add-controller.js"></script>
        <script src="app/project/manage/add-configuration-dialog-controller.js"></script>
        <script src="app/project/manage/manage-controller.js"></script>
        <script src="app/project/configure-controller.js"></script>
        <script src="app/project/list-controller.js"></script>

        <script src="app/reports/reports.js"></script>
        <script src="app/reports/status-controller.js"></script>

        <script src="app/session/session.js"></script>
        <script src="app/session/events-controller.js"></script>
        <script src="app/session/sessions-directive.js"></script>

        <script src="app/stack/stack.js"></script>
        <script src="app/stack/stack-controller.js"></script>
        <script src="app/stack/add-reference-dialog-controller.js"></script>

        <script src="app/status/status.js"></script>
        <script src="app/status/status-controller.js"></script>
        <!-- Add New Component JS Above -->
    </body>
</html>
