<div class="table-responsive" refresh-on="OrganizationChanged ProjectChanged" refresh-if="vm.canRefresh(data)" refresh-action="vm.get(vm.currentOptions)" refresh-throttle="10000">
  <table class="table table-striped table-bordered table-fixed b-t" refresh-on="PersistentEventChanged" refresh-if="vm.canRefresh(data)" refresh-action="vm.get(vm.currentOptions, false)" refresh-throttle="10000">
    <thead refresh-always="PlanChanged" refresh-action="vm.get(vm.currentOptions, false)" >
      <tr>
        <th>{{::'Name' | translate}}</th>
        <th ng-if="vm.includeOrganizationName">{{::'Organization' | translate}}</th>
        <th class="number hidden-xs"><abbr title="{{::'Total number of stacks' | translate}}">{{::'Stacks' | translate}}</abbr></th>
        <th class="number hidden-xs"><abbr title="{{::'Total number of events within the retention period' | translate}}">{{::'Events' | translate}}</abbr></th>
        <th class="action">{{::'Actions' | translate}}</th>
      </tr>
    </thead>
    <tbody>
      <tr class="row-clickable" ng-repeat="project in vm.projects | orderBy: 'name' track by project.id" ng-if="vm.hasProjects()">
        <td ng-click="vm.open(project.id, $event)">{{project.name}}</td>
        <td ng-if="vm.includeOrganizationName" ng-click="vm.open(project.id, $event)">{{project.organization_name}}</td>
        <td ng-click="vm.open(project.id, $event)" class="number hidden-xs">{{project.stack_count | number:0}}</td>
        <td ng-click="vm.open(project.id, $event)" class="number hidden-xs">{{project.event_count | number:0}}</td>
        <td>
          <div class="btn-group">
            <button type="button" role="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-fw fa-edit"></i> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" role="menu">
              <li><a ui-sref="app.project-frequent({ projectId: project.id })"><i class="fa fa-fw fa-signal"></i> {{::'Project Most Frequent' | translate}}</a></li>
              <li><a ui-sref="app.project.manage({ id: project.id })"><i class="fa fa-fw fa-edit"></i> {{::'Edit' | translate}}</a></li>
              <li><a ui-sref="app.project.configure({ id: project.id })"><i class="fa fa-fw fa-cloud-download"></i> {{::'Download & Configure Client' | translate}}</a></li>
              <li><a ng-click="vm.remove(project)"><i class="fa fa-fw fa-times"></i> {{::'Delete' | translate}}</a></li>
              <li class="divider"></li>
              <li><a ui-sref="app.organization.manage({ id: project.organization_id })"><i class="fa fa-fw fa-group"></i> {{::'View Organization' | translate}}</a></li>
            </ul>
          </div>
        </td>
      </tr>
      <tr ng-if="!vm.hasProjects() && vm.includeOrganizationName">
        <td class="hidden-xs" colspan="5">
          <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
          <strong ng-if="!vm.loading">{{vm.hasFilter ? 'No projects were found with the current filter.': 'No projects were found.' | translate}}</strong>
        </td>
        <td class="visible-xs" colspan="3">
          <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
          <strong ng-if="!vm.loading">{{vm.hasFilter ? 'No projects were found with the current filter.': 'No projects were found.'}}</strong>
        </td>
      </tr>
      <tr ng-if="!vm.hasProjects() && !vm.includeOrganizationName">
        <td class="hidden-xs" colspan="4">
          <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
          <strong ng-if="!vm.loading">{{vm.hasFilter ? 'No projects were found with the current filter.': 'No projects were found.'}}</strong>
        </td>
        <td class="visible-xs" colspan="2">
          <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
          <strong ng-if="!vm.loading">{{vm.hasFilter ? 'No projects were found with the current filter.': 'No projects were found.'}}</strong>
        </td>
      </tr>
    </tbody>
  </table>

  <div class="table-footer" ng-if="vm.previous || vm.next">
    <div class="row">
      <div class="col-sm-8 col-xs-8 text-center" ng-if="vm.pageSummary">
        <small class="text-muted inline m-t-xs">{{vm.pageSummary}}</small>
      </div>
      <div class="col-sm-4 col-xs-4 text-right">
        <ul class="pagination pagination-sm m-t-none m-b-none">
          <li ng-show="vm.currentOptions.page && vm.currentOptions.page > 2"><a ng-click="vm.get()"><i class="fa fa-fast-backward"></i></a></li>
          <li ng-class="{'disabled': !vm.previous}"><a ng-disabled="!vm.previous" ng-click="!vm.previous || vm.previousPage()"><i class="fa fa-chevron-left"></i></a></li>
          <li ng-class="{'disabled': !vm.next}"><a ng-disabled="!vm.next" ng-click="!vm.next || vm.nextPage()"><i class="fa fa-chevron-right"></i></a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
