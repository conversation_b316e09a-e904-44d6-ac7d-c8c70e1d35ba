{"name": "exceptionless", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "exceptionless", "dependencies": {"gulp": "4.0.2"}, "devDependencies": {"bower": "1.8.8", "grunt": "1.0.4", "grunt-angular-templates": "1.2.0", "grunt-browser-output": "1.0.3", "grunt-cache-bust": "1.7.0", "grunt-contrib-clean": "2.0.0", "grunt-contrib-compress": "1.6.0", "grunt-contrib-concat": "1.0.1", "grunt-contrib-connect": "2.1.0", "grunt-contrib-copy": "1.0.0", "grunt-contrib-cssmin": "3.0.0", "grunt-contrib-htmlmin": "3.1.0", "grunt-contrib-jshint": "2.1.0", "grunt-contrib-less": "2.0.0", "grunt-contrib-uglify": "4.0.1", "grunt-contrib-watch": "1.1.0", "grunt-dom-munger": "3.4.0", "grunt-gh-pages": "3.1.0", "grunt-html-angular-validate": "0.6.1", "grunt-karma": "3.0.2", "grunt-ng-annotate": "3.0.0", "grunt-replace": "1.0.1", "grunt-traceur": "0.5.5", "jasmine-core": "3.5.0", "jshint-stylish": "2.2.1", "karma": "4.4.1", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.1.1", "karma-junit-reporter": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-ng-html2js-preprocessor": "1.0.0", "load-grunt-config": "3.0.1", "puppeteer": "2.1.1", "rimraf": "3.0.2", "streamqueue": "1.1.2", "traceur": "0.0.111"}}, "node_modules/@types/mime-types": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/@types/minimatch": {"version": "3.0.3", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "8.10.52", "dev": true, "license": "MIT"}, "node_modules/abbrev": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/accepts": {"version": "1.3.7", "dev": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "2.6.4", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/after": {"version": "0.8.2", "dev": true, "license": "MIT"}, "node_modules/agent-base": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/ajv": {"version": "6.10.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "node_modules/alter": {"version": "0.2.0", "dev": true, "license": "MIT", "dependencies": {"stable": "~0.1.3"}}, "node_modules/amdefine": {"version": "1.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON> OR MIT", "engines": {"node": ">=0.4.2"}}, "node_modules/ansi-colors": {"version": "1.1.0", "license": "MIT", "dependencies": {"ansi-wrap": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-gray": {"version": "0.1.1", "license": "MIT", "dependencies": {"ansi-wrap": "0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-regex": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-to-html": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/ansi-wrap": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch": {"version": "3.1.1", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/anymatch/node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/append-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"buffer-equal": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/applause": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"cson-parser": "^1.1.0", "js-yaml": "^3.3.0", "lodash": "^3.10.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/applause/node_modules/lodash": {"version": "3.10.1", "dev": true, "license": "MIT"}, "node_modules/aproba": {"version": "1.2.0", "dev": true, "license": "ISC", "optional": true}, "node_modules/archiver": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "walkdir": "^0.0.11", "zip-stream": "^1.1.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/archiver-utils": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"glob": "^7.0.0", "graceful-fs": "^4.1.0", "lazystream": "^1.0.0", "lodash": "^4.8.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/archiver/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/archiver/node_modules/async/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/archy": {"version": "1.0.0", "license": "MIT"}, "node_modules/are-we-there-yet": {"version": "1.1.5", "dev": true, "license": "ISC", "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/argparse": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/arr-filter": {"version": "1.1.2", "license": "MIT", "dependencies": {"make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-map": {"version": "2.0.2", "license": "MIT", "dependencies": {"make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-differ": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array-each": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-find-index": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-initial": {"version": "1.1.0", "license": "MIT", "dependencies": {"array-slice": "^1.0.0", "is-number": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-initial/node_modules/array-slice": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-initial/node_modules/is-number": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-last": {"version": "1.3.0", "license": "MIT", "dependencies": {"is-number": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-last/node_modules/is-number": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-sort": {"version": "1.0.0", "license": "MIT", "dependencies": {"default-compare": "^1.0.0", "get-value": "^2.0.6", "kind-of": "^5.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-sort/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/arraybuffer.slice": {"version": "0.0.7", "dev": true, "license": "MIT"}, "node_modules/arrify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/asap": {"version": "2.0.6", "dev": true, "license": "MIT", "optional": true}, "node_modules/asn1": {"version": "0.2.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert": {"version": "1.4.1", "dev": true, "license": "MIT", "dependencies": {"util": "0.10.3"}}, "node_modules/assert-plus": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/assign-symbols": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ast-types": {"version": "0.13.2", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/async": {"version": "1.5.2", "dev": true, "license": "MIT"}, "node_modules/async-done": {"version": "1.3.2", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.2", "process-nextick-args": "^2.0.0", "stream-exhaust": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/async-each": {"version": "1.0.1", "license": "MIT"}, "node_modules/async-limiter": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/async-settle": {"version": "1.0.0", "license": "MIT", "dependencies": {"async-done": "^1.2.2"}, "engines": {"node": ">= 0.10"}}, "node_modules/asynckit": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/aws-sign2": {"version": "0.7.0", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.8.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/bach": {"version": "1.2.0", "license": "MIT", "dependencies": {"arr-filter": "^1.1.1", "arr-flatten": "^1.0.1", "arr-map": "^2.0.0", "array-each": "^1.0.0", "array-initial": "^1.0.0", "array-last": "^1.1.1", "async-done": "^1.2.2", "async-settle": "^1.0.0", "now-and-later": "^2.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/backo2": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.0", "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "license": "MIT", "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/is-accessor-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/is-data-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/is-descriptor": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/base64-arraybuffer": {"version": "0.1.5", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/base64-js": {"version": "1.3.1", "dev": true, "license": "MIT"}, "node_modules/base64id": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/basic-auth": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/batch": {"version": "0.6.1", "dev": true, "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/beeper": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/better-assert": {"version": "1.0.2", "dev": true, "dependencies": {"callsite": "1.0.0"}, "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "1.11.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/bl": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "node_modules/blob": {"version": "0.0.5", "dev": true, "license": "MIT"}, "node_modules/bluebird": {"version": "3.7.2", "dev": true, "license": "MIT"}, "node_modules/body": {"version": "5.1.0", "dev": true, "dependencies": {"continuable-cache": "^0.3.1", "error": "^7.0.0", "raw-body": "~1.1.0", "safe-json-parse": "~1.0.1"}}, "node_modules/body-parser": {"version": "1.19.0", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/bytes": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/http-errors": {"version": "1.7.2", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/body-parser/node_modules/qs": {"version": "6.7.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/body-parser/node_modules/raw-body": {"version": "2.4.0", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/setprototypeof": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/bower": {"version": "1.8.8", "dev": true, "license": "MIT", "bin": {"bower": "bin/bower"}, "engines": {"node": ">=0.10.0"}}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/buffer": {"version": "5.4.3", "dev": true, "license": "MIT", "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}}, "node_modules/buffer-alloc": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "node_modules/buffer-alloc-unsafe": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-equal": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/buffer-fill": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/buffer-from": {"version": "1.0.0", "license": "MIT"}, "node_modules/builtin-modules": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/bytes": {"version": "1.0.0", "dev": true}, "node_modules/cache-base": {"version": "1.0.1", "license": "MIT", "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cache-base/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/callsite": {"version": "1.0.0", "dev": true, "engines": {"node": "*"}}, "node_modules/camel-case": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "node_modules/camelcase": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/camelcase-keys": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/caseless": {"version": "0.12.0", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/chalk": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cheerio": {"version": "0.12.4", "dev": true, "dependencies": {"cheerio-select": "*", "entities": "0.x", "htmlparser2": "3.1.4", "underscore": "~1.4"}, "engines": {"node": ">= 0.6"}}, "node_modules/cheerio-select": {"version": "0.0.3", "dev": true, "dependencies": {"CSSselect": "0.x"}, "engines": {"node": ">= 0.4.7"}}, "node_modules/cheerio/node_modules/domhandler": {"version": "2.0.3", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/cheerio/node_modules/domutils": {"version": "1.1.6", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/cheerio/node_modules/entities": {"version": "0.5.0", "dev": true, "license": "BSD-like"}, "node_modules/cheerio/node_modules/htmlparser2": {"version": "3.1.4", "dev": true, "dependencies": {"domelementtype": "1", "domhandler": "2.0", "domutils": "1.1", "readable-stream": "1.0"}}, "node_modules/cheerio/node_modules/isarray": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/cheerio/node_modules/readable-stream": {"version": "1.0.34", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/cheerio/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "license": "MIT"}, "node_modules/chokidar": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.3.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.1.2"}}, "node_modules/chokidar/node_modules/binary-extensions": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/is-binary-path": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/is-glob": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chokidar/node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/chokidar/node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/chokidar/node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/chownr": {"version": "1.1.4", "dev": true, "license": "ISC", "optional": true}, "node_modules/class-utils": {"version": "0.3.6", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/clean-css": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 4.0"}}, "node_modules/cli": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"exit": "0.1.2", "glob": "^7.1.1"}, "engines": {"node": ">=0.2.5"}}, "node_modules/cli/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/clone": {"version": "2.1.2", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clone-buffer": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/clone-stats": {"version": "1.0.0", "license": "MIT"}, "node_modules/cloneable-readable": {"version": "1.1.3", "license": "MIT", "dependencies": {"inherits": "^2.0.1", "process-nextick-args": "^2.0.0", "readable-stream": "^2.3.5"}}, "node_modules/co": {"version": "4.6.0", "dev": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/code-point-at": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/coffee-script": {"version": "1.10.0", "dev": true, "license": "MIT", "bin": {"cake": "bin/cake", "coffee": "bin/coffee"}, "engines": {"node": ">=0.8.0"}}, "node_modules/collection-map": {"version": "1.0.0", "license": "MIT", "dependencies": {"arr-map": "^2.0.2", "for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/collection-map/node_modules/for-own": {"version": "1.0.0", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/collection-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "1.9.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "^1.1.1"}}, "node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/color-support": {"version": "1.1.3", "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/colors": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.9.0", "dev": true, "license": "MIT", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "engines": {"node": ">= 0.6.x"}}, "node_modules/component-bind": {"version": "1.0.0", "dev": true}, "node_modules/component-emitter": {"version": "1.2.1", "license": "MIT"}, "node_modules/component-inherit": {"version": "0.0.3", "dev": true}, "node_modules/compress-commons": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "^0.2.1", "crc32-stream": "^2.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/connect": {"version": "3.7.0", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/connect-livereload": {"version": "0.6.1", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/console-browserify": {"version": "1.1.0", "dev": true, "dependencies": {"date-now": "^0.1.4"}}, "node_modules/console-control-strings": {"version": "1.1.0", "dev": true, "license": "ISC", "optional": true}, "node_modules/content-type": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/continuable-cache": {"version": "0.3.1", "dev": true}, "node_modules/convert-source-map": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "0.3.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookiejar": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/copy-descriptor": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/copy-props": {"version": "2.0.4", "license": "MIT", "dependencies": {"each-props": "^1.3.0", "is-plain-object": "^2.0.1"}}, "node_modules/core-util-is": {"version": "1.0.2", "license": "MIT"}, "node_modules/crc": {"version": "3.8.0", "dev": true, "license": "MIT", "dependencies": {"buffer": "^5.1.0"}}, "node_modules/crc32-stream": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"crc": "^3.4.4", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/cson": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"coffee-script": "^1.12.7", "cson-parser": "^1.3.4", "editions": "^1.3.3", "extract-opts": "^3.3.1", "requirefresh": "^2.1.0", "safefs": "^4.1.0"}, "bin": {"cson2json": "bin/cson2json", "json2cson": "bin/json2cson"}, "engines": {"node": ">=0.8"}}, "node_modules/cson-parser": {"version": "1.3.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"coffee-script": "^1.10.0"}}, "node_modules/cson/node_modules/coffee-script": {"version": "1.12.7", "dev": true, "license": "MIT", "bin": {"cake": "bin/cake", "coffee": "bin/coffee"}, "engines": {"node": ">=0.8.0"}}, "node_modules/CSSselect": {"version": "0.7.0", "dev": true, "license": "BSD-like", "dependencies": {"boolbase": "~1.0.0", "CSSwhat": "0.4", "domutils": "1.4", "nth-check": "~1.0.0"}}, "node_modules/CSSselect/node_modules/domutils": {"version": "1.4.3", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/CSSwhat": {"version": "0.4.7", "dev": true, "license": "BSD-like", "engines": {"node": "*"}}, "node_modules/currently-unhandled": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"array-find-index": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/custom-event": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/d": {"version": "1.0.1", "license": "ISC", "dependencies": {"es5-ext": "^0.10.50", "type": "^1.0.1"}}, "node_modules/dashdash": {"version": "1.14.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-uri-to-buffer": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"@types/node": "^8.0.7"}}, "node_modules/date-format": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/date-now": {"version": "0.1.4", "dev": true}, "node_modules/dateformat": {"version": "1.0.12", "dev": true, "license": "MIT", "dependencies": {"get-stdin": "^4.0.1", "meow": "^3.3.0"}, "bin": {"dateformat": "bin/cli.js"}, "engines": {"node": "*"}}, "node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/decamelize": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decode-uri-component": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/decompress-response": {"version": "4.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mimic-response": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/deep-extend": {"version": "0.6.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4.0.0"}}, "node_modules/deep-is": {"version": "0.1.3", "dev": true, "license": "MIT"}, "node_modules/default-compare": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^5.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/default-compare/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-resolution": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/define-properties": {"version": "1.1.2", "license": "MIT", "dependencies": {"foreach": "^2.0.5", "object-keys": "^1.0.8"}, "engines": {"node": ">= 0.4"}}, "node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-accessor-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-data-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-descriptor": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/degenerator": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"ast-types": "0.x.x", "escodegen": "1.x.x", "esprima": "3.x.x"}}, "node_modules/degenerator/node_modules/esprima": {"version": "3.1.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/depd": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/detect-file": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/detect-libc": {"version": "1.0.3", "dev": true, "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/di": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/dom-serialize": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"custom-event": "~1.0.0", "ent": "~2.2.0", "extend": "^3.0.0", "void-elements": "^2.0.0"}}, "node_modules/dom-serializer": {"version": "0.2.1", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}}, "node_modules/dom-serializer/node_modules/domelementtype": {"version": "2.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/dom-serializer/node_modules/entities": {"version": "2.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domelementtype": {"version": "1.3.0", "dev": true}, "node_modules/domhandler": {"version": "2.3.0", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/domutils": {"version": "1.5.1", "dev": true, "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/duplexer": {"version": "0.1.1", "dev": true}, "node_modules/duplexify": {"version": "3.7.1", "license": "MIT", "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/each-props": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.1", "object.defaults": "^1.1.0"}}, "node_modules/eachr": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"editions": "^2.2.0", "typechecker": "^4.9.0"}, "engines": {"node": ">=0.10"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/eachr/node_modules/editions": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"errlop": "^2.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/eachr/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/editions": {"version": "1.3.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/ee-first": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.1", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/engine.io": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "base64id": "1.0.0", "cookie": "0.3.1", "debug": "~3.1.0", "engine.io-parser": "~2.1.0", "ws": "~3.3.1"}}, "node_modules/engine.io-client": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"component-emitter": "1.2.1", "component-inherit": "0.0.3", "debug": "~3.1.0", "engine.io-parser": "~2.1.1", "has-cors": "1.1.0", "indexof": "0.0.1", "parseqs": "0.0.5", "parseuri": "0.0.5", "ws": "~3.3.1", "xmlhttprequest-ssl": "~1.5.4", "yeast": "0.1.2"}}, "node_modules/engine.io-client/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/engine.io-client/node_modules/ws": {"version": "3.3.3", "dev": true, "license": "MIT", "dependencies": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}, "node_modules/engine.io-parser": {"version": "2.1.3", "dev": true, "license": "MIT", "dependencies": {"after": "0.8.2", "arraybuffer.slice": "~0.0.7", "base64-arraybuffer": "0.1.5", "blob": "0.0.5", "has-binary2": "~1.0.2"}}, "node_modules/engine.io/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/engine.io/node_modules/ws": {"version": "3.3.3", "dev": true, "license": "MIT", "dependencies": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}, "node_modules/ent": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/entities": {"version": "1.0.0", "dev": true, "license": "BSD-like"}, "node_modules/errlop": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/errno": {"version": "0.1.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/error": {"version": "7.0.2", "dev": true, "dependencies": {"string-template": "~0.2.1", "xtend": "~4.0.0"}}, "node_modules/error-ex": {"version": "1.3.1", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es5-ext": {"version": "0.10.50", "license": "ISC", "dependencies": {"es6-iterator": "~2.0.3", "es6-symbol": "~3.1.1", "next-tick": "^1.0.0"}}, "node_modules/es6-iterator": {"version": "2.0.3", "license": "MIT", "dependencies": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "node_modules/es6-promise": {"version": "4.2.8", "dev": true, "license": "MIT"}, "node_modules/es6-promisify": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"es6-promise": "^4.0.3"}}, "node_modules/es6-set": {"version": "0.1.5", "dev": true, "license": "MIT", "dependencies": {"d": "1", "es5-ext": "~0.10.14", "es6-iterator": "~2.0.1", "es6-symbol": "3.1.1", "event-emitter": "~0.3.5"}}, "node_modules/es6-symbol": {"version": "3.1.1", "license": "MIT", "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/es6-weak-map": {"version": "2.0.3", "license": "ISC", "dependencies": {"d": "1", "es5-ext": "^0.10.46", "es6-iterator": "^2.0.3", "es6-symbol": "^3.1.1"}}, "node_modules/escape-html": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/escodegen": {"version": "1.12.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^3.1.3", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=4.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/escodegen/node_modules/esprima": {"version": "3.1.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esprima": {"version": "2.7.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-emitter": {"version": "0.3.5", "dev": true, "license": "MIT", "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/eventemitter2": {"version": "0.4.14", "dev": true, "license": "MIT"}, "node_modules/eventemitter3": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.x"}}, "node_modules/exit": {"version": "0.1.2", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expand-template": {"version": "2.0.3", "dev": true, "license": "(MIT OR WTFPL)", "optional": true, "engines": {"node": ">=6"}}, "node_modules/expand-tilde": {"version": "2.0.2", "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extend": {"version": "3.0.1", "license": "MIT"}, "node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extend-shallow/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extract-opts": {"version": "3.4.0", "dev": true, "license": "MIT", "dependencies": {"eachr": "^3.2.0", "editions": "^2.2.0", "typechecker": "^4.9.0"}, "engines": {"node": ">=0.10"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/extract-opts/node_modules/editions": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"errlop": "^2.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/extract-opts/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/extract-zip": {"version": "1.6.7", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "1.6.2", "debug": "2.6.9", "mkdirp": "0.5.1", "yauzl": "2.4.1"}, "bin": {"extract-zip": "cli.js"}}, "node_modules/extsprintf": {"version": "1.3.0", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "optional": true}, "node_modules/fancy-log": {"version": "1.3.3", "license": "MIT", "dependencies": {"ansi-gray": "^0.1.1", "color-support": "^1.1.3", "parse-node-version": "^1.0.0", "time-stamp": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/fast-deep-equal": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/fast-json-stable-stringify": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/faye-websocket": {"version": "0.10.0", "dev": true, "license": "MIT", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.4.0"}}, "node_modules/fd-slicer": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "node_modules/figures": {"version": "1.7.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5", "object-assign": "^4.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/file-sync-cmp": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/file-uri-to-path": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/filendir": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"mkdirp": "^0.5.0"}, "engines": {"node": ">=4"}}, "node_modules/finalhandler": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "1.1.2", "license": "MIT", "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/findup-sync": {"version": "0.3.0", "dev": true, "dependencies": {"glob": "~5.0.0"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/findup-sync/node_modules/glob": {"version": "5.0.15", "dev": true, "license": "ISC", "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/fined": {"version": "1.2.0", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/flagged-respawn": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/flatted": {"version": "2.0.1", "dev": true, "license": "ISC"}, "node_modules/flush-write-stream": {"version": "1.1.1", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "node_modules/follow-redirects": {"version": "1.10.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.0.0"}, "engines": {"node": ">=4.0"}}, "node_modules/follow-redirects/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/follow-redirects/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/for-in": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/foreach": {"version": "2.0.5", "license": "MIT"}, "node_modules/forever-agent": {"version": "0.6.1", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.3.3", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/formidable": {"version": "1.2.1", "dev": true, "license": "MIT"}, "node_modules/fragment-cache": {"version": "0.2.1", "license": "MIT", "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fresh": {"version": "0.5.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-constants": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/fs-extra": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "node_modules/fs-mkdirp-stream": {"version": "1.0.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.11", "through2": "^2.0.3"}, "engines": {"node": ">= 0.10"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/fsevents": {"version": "2.1.2", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/ftp": {"version": "0.3.10", "dev": true, "dependencies": {"readable-stream": "1.1.x", "xregexp": "2.0.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/ftp/node_modules/isarray": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/ftp/node_modules/readable-stream": {"version": "1.1.14", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/ftp/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "license": "MIT"}, "node_modules/function-bind": {"version": "1.1.1", "license": "MIT"}, "node_modules/gauge": {"version": "2.7.4", "dev": true, "license": "ISC", "optional": true, "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/gaze": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"globule": "^1.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/get-caller-file": {"version": "1.0.3", "license": "ISC"}, "node_modules/get-stdin": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/get-uri": {"version": "2.0.3", "dev": true, "license": "MIT", "dependencies": {"data-uri-to-buffer": "2", "debug": "4", "extend": "~3.0.2", "file-uri-to-path": "1", "ftp": "~0.3.10", "readable-stream": "3"}}, "node_modules/get-uri/node_modules/debug": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/get-uri/node_modules/extend": {"version": "3.0.2", "dev": true, "license": "MIT"}, "node_modules/get-uri/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/get-uri/node_modules/readable-stream": {"version": "3.4.0", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/get-value": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/getobject": {"version": "0.1.0", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/getpass": {"version": "0.1.7", "dev": true, "license": "MIT", "optional": true, "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/github-from-package": {"version": "0.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/glob": {"version": "7.0.6", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.2", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glob-parent": {"version": "5.1.0", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-parent/node_modules/is-glob": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-stream": {"version": "6.1.0", "license": "MIT", "dependencies": {"extend": "^3.0.0", "glob": "^7.1.1", "glob-parent": "^3.1.0", "is-negated-glob": "^1.0.0", "ordered-read-streams": "^1.0.0", "pumpify": "^1.3.5", "readable-stream": "^2.1.5", "remove-trailing-separator": "^1.0.1", "to-absolute-glob": "^2.0.0", "unique-stream": "^2.0.2"}, "engines": {"node": ">= 0.10"}}, "node_modules/glob-stream/node_modules/glob": {"version": "7.1.4", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glob-stream/node_modules/glob-parent": {"version": "3.1.0", "license": "ISC", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/glob-stream/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-stream/node_modules/is-glob": {"version": "3.1.0", "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher": {"version": "5.0.3", "license": "MIT", "dependencies": {"anymatch": "^2.0.0", "async-done": "^1.2.0", "chokidar": "^2.0.0", "is-negated-glob": "^1.0.0", "just-debounce": "^1.0.0", "object.defaults": "^1.1.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/glob-watcher/node_modules/anymatch": {"version": "2.0.0", "license": "ISC", "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/glob-watcher/node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/braces": {"version": "2.3.2", "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/chokidar": {"version": "2.1.8", "license": "MIT", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}}, "node_modules/glob-watcher/node_modules/chokidar/node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/is-accessor-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/is-data-descriptor": {"version": "0.1.4", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/expand-brackets/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fill-range": {"version": "4.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents": {"version": "1.2.9", "bundleDependencies": ["node-pre-gyp"], "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"nan": "^2.12.1", "node-pre-gyp": "^0.12.0"}, "engines": {"node": ">=4.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/abbrev": {"version": "1.1.1", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/ansi-regex": {"version": "2.1.1", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/aproba": {"version": "1.2.0", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/are-we-there-yet": {"version": "1.1.5", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/balanced-match": {"version": "1.0.0", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/brace-expansion": {"version": "1.1.11", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/chownr": {"version": "1.1.1", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/code-point-at": {"version": "1.1.0", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/console-control-strings": {"version": "1.1.0", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/core-util-is": {"version": "1.0.2", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/debug": {"version": "4.1.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"ms": "^2.1.1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/deep-extend": {"version": "0.6.0", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=4.0.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/delegates": {"version": "1.0.0", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/detect-libc": {"version": "1.0.3", "inBundle": true, "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/fs-minipass": {"version": "1.2.5", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"minipass": "^2.2.1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/fs.realpath": {"version": "1.0.0", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/gauge": {"version": "2.7.4", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/glob": {"version": "7.1.3", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/has-unicode": {"version": "2.0.1", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/iconv-lite": {"version": "0.4.24", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/ignore-walk": {"version": "3.0.1", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"minimatch": "^3.0.4"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/inflight": {"version": "1.0.6", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/inherits": {"version": "2.0.3", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/ini": {"version": "1.3.5", "inBundle": true, "license": "ISC", "optional": true, "engines": {"node": "*"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/isarray": {"version": "1.0.0", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/minimatch": {"version": "3.0.4", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/minimist": {"version": "0.0.8", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/minipass": {"version": "2.3.5", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/minizlib": {"version": "1.2.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"minipass": "^2.2.1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/mkdirp": {"version": "0.5.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/ms": {"version": "2.1.1", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/needle": {"version": "2.3.0", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"debug": "^4.1.0", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}, "bin": {"needle": "bin/needle"}, "engines": {"node": ">= 4.4.x"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/node-pre-gyp": {"version": "0.12.0", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"detect-libc": "^1.0.2", "mkdirp": "^0.5.1", "needle": "^2.2.1", "nopt": "^4.0.1", "npm-packlist": "^1.1.6", "npmlog": "^4.0.2", "rc": "^1.2.7", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^4"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/nopt": {"version": "4.0.1", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/npm-bundled": {"version": "1.0.6", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/npm-packlist": {"version": "1.4.1", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/npmlog": {"version": "4.1.2", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/number-is-nan": {"version": "1.0.1", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/object-assign": {"version": "4.1.1", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/once": {"version": "1.4.0", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"wrappy": "1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/os-homedir": {"version": "1.0.2", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/os-tmpdir": {"version": "1.0.2", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/osenv": {"version": "0.1.5", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/path-is-absolute": {"version": "1.0.1", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/process-nextick-args": {"version": "2.0.0", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/rc": {"version": "1.2.8", "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "optional": true, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/rc/node_modules/minimist": {"version": "1.2.0", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/readable-stream": {"version": "2.3.6", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/rimraf": {"version": "2.6.3", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/safe-buffer": {"version": "5.1.2", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/safer-buffer": {"version": "2.1.2", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/sax": {"version": "1.2.4", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/semver": {"version": "5.7.0", "inBundle": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/set-blocking": {"version": "2.0.0", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/signal-exit": {"version": "3.0.2", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/string_decoder": {"version": "1.1.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/string-width": {"version": "1.0.2", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/strip-ansi": {"version": "3.0.1", "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/strip-json-comments": {"version": "2.0.1", "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/tar": {"version": "4.4.8", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "engines": {"node": ">=4.5"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/util-deprecate": {"version": "1.0.2", "inBundle": true, "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/wide-align": {"version": "1.1.3", "inBundle": true, "license": "ISC", "optional": true, "dependencies": {"string-width": "^1.0.2 || 2"}}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/wrappy": {"version": "1.0.2", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/fsevents/node_modules/yallist": {"version": "3.0.3", "inBundle": true, "license": "ISC", "optional": true}, "node_modules/glob-watcher/node_modules/glob-parent": {"version": "3.1.0", "license": "ISC", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/glob-watcher/node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-accessor-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-data-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-descriptor": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-glob": {"version": "4.0.1", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-watcher/node_modules/nan": {"version": "2.14.0", "license": "MIT", "optional": true}, "node_modules/glob-watcher/node_modules/readdirp": {"version": "2.2.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "engines": {"node": ">=0.10"}}, "node_modules/global-modules": {"version": "1.0.0", "license": "MIT", "dependencies": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/global-prefix": {"version": "1.0.2", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globule": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"glob": "~7.1.1", "lodash": "~4.17.10", "minimatch": "~3.0.2"}, "engines": {"node": ">= 0.10"}}, "node_modules/globule/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glogg": {"version": "1.0.2", "license": "MIT", "dependencies": {"sparkles": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/graceful-fs": {"version": "4.1.11", "license": "ISC", "engines": {"node": ">=0.4.0"}}, "node_modules/graceful-readlink": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/grunt": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"coffeescript": "~1.10.0", "dateformat": "~1.0.12", "eventemitter2": "~0.4.13", "exit": "~0.1.1", "findup-sync": "~0.3.0", "glob": "~7.0.0", "grunt-cli": "~1.2.0", "grunt-known-options": "~1.1.0", "grunt-legacy-log": "~2.0.0", "grunt-legacy-util": "~1.1.1", "iconv-lite": "~0.4.13", "js-yaml": "~3.13.0", "minimatch": "~3.0.2", "mkdirp": "~0.5.1", "nopt": "~3.0.6", "path-is-absolute": "~1.0.0", "rimraf": "~2.6.2"}, "bin": {"grunt": "bin/grunt"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-angular-templates": {"version": "1.2.0", "dev": true, "dependencies": {"html-minifier": "~4.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-browser-output": {"version": "1.0.3", "dev": true, "dependencies": {"ansi-to-html": "~0.1.1", "hooker": "~0.2.3", "ws": "~0.4.31"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-cache-bust": {"version": "1.7.0", "dev": true, "dependencies": {"fs-extra": "^6.0.1"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"grunt": ">=0.4.0"}}, "node_modules/grunt-contrib-clean": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.1", "rimraf": "^2.6.2"}, "engines": {"node": ">=6"}, "peerDependencies": {"grunt": ">=0.4.5"}}, "node_modules/grunt-contrib-clean/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/grunt-contrib-clean/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/grunt-contrib-clean/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/grunt-contrib-clean/node_modules/rimraf": {"version": "2.7.1", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/grunt-contrib-compress": {"version": "1.6.0", "dev": true, "license": "MIT", "dependencies": {"archiver": "^1.3.0", "chalk": "^1.1.1", "lodash": "^4.7.0", "pretty-bytes": "^4.0.2", "stream-buffers": "^2.1.0"}, "engines": {"node": ">=4.0"}, "optionalDependencies": {"iltorb": "^2.4.3"}}, "node_modules/grunt-contrib-concat": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"chalk": "^1.0.0", "source-map": "^0.5.3"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"grunt": ">=0.4.0"}}, "node_modules/grunt-contrib-concat/node_modules/source-map": {"version": "0.5.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-connect": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.1", "connect": "^3.6.6", "connect-livereload": "^0.6.0", "morgan": "^1.9.1", "node-http2": "^4.0.1", "opn": "^5.3.0", "portscanner": "^2.2.0", "serve-index": "^1.9.1", "serve-static": "^1.13.2"}, "engines": {"node": ">=6"}, "peerDependencies": {"grunt": ">=0.4.0"}}, "node_modules/grunt-contrib-connect/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/grunt-contrib-connect/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/grunt-contrib-copy": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^1.1.1", "file-sync-cmp": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-cssmin": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.1", "clean-css": "~4.2.1", "maxmin": "^2.1.0"}, "engines": {"node": ">=6.0"}}, "node_modules/grunt-contrib-cssmin/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-cssmin/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-cssmin/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-htmlmin": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.2", "html-minifier": "^4.0.0", "pretty-bytes": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/grunt-contrib-htmlmin/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-htmlmin/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-htmlmin/node_modules/pretty-bytes": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/grunt-contrib-htmlmin/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-jshint": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.2", "hooker": "^0.2.3", "jshint": "~2.10.2"}, "engines": {"node": ">=6"}, "peerDependencies": {"grunt": ">=1.0.3"}}, "node_modules/grunt-contrib-jshint/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-jshint/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-jshint/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-less": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"async": "^2.0.0", "chalk": "^1.0.0", "less": "^3.0.4", "lodash": "^4.17.10"}, "engines": {"node": ">=6"}}, "node_modules/grunt-contrib-less/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/grunt-contrib-less/node_modules/async/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/grunt-contrib-uglify": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.1", "maxmin": "^2.1.0", "uglify-js": "^3.5.0", "uri-path": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/grunt-contrib-uglify/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-uglify/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-uglify/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-contrib-watch": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.0", "gaze": "^1.1.0", "lodash": "^4.17.10", "tiny-lr": "^1.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-watch/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/grunt-contrib-watch/node_modules/async/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/grunt-dom-munger": {"version": "3.4.0", "dev": true, "dependencies": {"cheerio": "~0.12.3"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.1"}}, "node_modules/grunt-gh-pages": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"async": "2.0.1", "fs-extra": "^0.30.0", "graceful-fs": "4.1.5", "q": "0.9.3", "q-io": "^1.13.6", "url-safe": "^2.0.0"}, "peerDependencies": {"grunt": ">=0.4.0"}}, "node_modules/grunt-gh-pages/node_modules/async": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.8.0"}}, "node_modules/grunt-gh-pages/node_modules/fs-extra": {"version": "0.30.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0", "path-is-absolute": "^1.0.0", "rimraf": "^2.2.8"}}, "node_modules/grunt-gh-pages/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/grunt-gh-pages/node_modules/graceful-fs": {"version": "4.1.5", "dev": true, "license": "ISC", "engines": {"node": ">=0.4.0"}}, "node_modules/grunt-gh-pages/node_modules/jsonfile": {"version": "2.4.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/grunt-gh-pages/node_modules/jsonfile/node_modules/graceful-fs": {"version": "4.2.2", "dev": true, "license": "ISC", "optional": true}, "node_modules/grunt-gh-pages/node_modules/rimraf": {"version": "2.7.1", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/grunt-html-angular-validate": {"version": "0.6.1", "dev": true, "license": "MIT", "dependencies": {"colors": "~1.1.2", "html-angular-validate": "~0.2.3"}, "engines": {"node": ">= 5.0.0"}}, "node_modules/grunt-karma": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.10"}, "peerDependencies": {"grunt": ">=0.4.x", "karma": "^3.0.0 || ^4.0.0"}}, "node_modules/grunt-known-options": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-legacy-log": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"colors": "~1.1.2", "grunt-legacy-log-utils": "~2.0.0", "hooker": "~0.2.3", "lodash": "~4.17.5"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/grunt-legacy-log-utils": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"chalk": "~2.4.1", "lodash": "~4.17.10"}, "engines": {"node": ">=6"}}, "node_modules/grunt-legacy-log-utils/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-legacy-log-utils/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-legacy-log-utils/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/grunt-legacy-util": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"async": "~1.5.2", "exit": "~0.1.1", "getobject": "~0.1.0", "hooker": "~0.2.3", "lodash": "~4.17.10", "underscore.string": "~3.3.4", "which": "~1.3.0"}, "engines": {"node": ">= 6"}}, "node_modules/grunt-legacy-util/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/grunt-ng-annotate": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"lodash.clonedeep": "^4.5.0", "ng-annotate": "^1.2.1"}, "engines": {"node": ">=4.4 <5 || >=6.9"}, "peerDependencies": {"grunt": ">=0.4.5"}}, "node_modules/grunt-replace": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"applause": "1.2.2", "chalk": "^1.1.0", "file-sync-cmp": "^0.1.0", "lodash": "^4.11.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-traceur": {"version": "0.5.5", "dev": true, "dependencies": {"es6-promise": "^1.0.0", "lodash": "^2.4.1"}, "engines": {"node": ">=0.8.0"}, "peerDependencies": {"grunt": ">=0.4.0", "traceur": ">=0.0.88"}}, "node_modules/grunt-traceur/node_modules/es6-promise": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/grunt-traceur/node_modules/lodash": {"version": "2.4.2", "dev": true, "engines": ["node", "rhino"], "license": "MIT"}, "node_modules/grunt/node_modules/coffeescript": {"version": "1.10.0", "dev": true, "license": "MIT", "bin": {"cake": "bin/cake", "coffee": "bin/coffee"}, "engines": {"node": ">=0.8.0"}}, "node_modules/grunt/node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/grunt/node_modules/grunt-cli": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"findup-sync": "~0.3.0", "grunt-known-options": "~1.1.0", "nopt": "~3.0.6", "resolve": "~1.1.0"}, "bin": {"grunt": "bin/grunt"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt/node_modules/js-yaml": {"version": "3.13.1", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/grunt/node_modules/rimraf": {"version": "2.6.3", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/grunt/node_modules/rimraf/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/gulp": {"version": "4.0.2", "license": "MIT", "dependencies": {"glob-watcher": "^5.0.3", "gulp-cli": "^2.2.0", "undertaker": "^1.2.1", "vinyl-fs": "^3.0.0"}, "bin": {"gulp": "bin/gulp.js"}, "engines": {"node": ">= 0.10"}}, "node_modules/gulp/node_modules/camelcase": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/gulp/node_modules/cliui": {"version": "3.2.0", "license": "ISC", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "node_modules/gulp/node_modules/gulp-cli": {"version": "2.2.0", "license": "MIT", "dependencies": {"ansi-colors": "^1.0.1", "archy": "^1.0.0", "array-sort": "^1.0.0", "color-support": "^1.1.3", "concat-stream": "^1.6.0", "copy-props": "^2.0.1", "fancy-log": "^1.3.2", "gulplog": "^1.0.0", "interpret": "^1.1.0", "isobject": "^3.0.1", "liftoff": "^3.1.0", "matchdep": "^2.0.0", "mute-stdout": "^1.0.0", "pretty-hrtime": "^1.0.0", "replace-homedir": "^1.0.0", "semver-greatest-satisfied-range": "^1.1.0", "v8flags": "^3.0.1", "yargs": "^7.1.0"}, "bin": {"gulp": "bin/gulp.js"}, "engines": {"node": ">= 0.10"}}, "node_modules/gulp/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/gulp/node_modules/yargs": {"version": "7.1.0", "license": "MIT", "dependencies": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.0"}}, "node_modules/gulplog": {"version": "1.0.0", "license": "MIT", "dependencies": {"glogg": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/gzip-size": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"duplexer": "^0.1.1"}, "engines": {"node": ">=0.12.0"}}, "node_modules/har-schema": {"version": "2.0.0", "dev": true, "license": "ISC", "optional": true, "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ajv": "^6.5.5", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-ansi": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-binary2": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"isarray": "2.0.1"}}, "node_modules/has-binary2/node_modules/isarray": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/has-cors": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-symbols": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/has-unicode": {"version": "2.0.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/has-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-value/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/he": {"version": "1.2.0", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/homedir-polyfill": {"version": "1.0.3", "license": "MIT", "dependencies": {"parse-passwd": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hooker": {"version": "0.2.3", "dev": true, "engines": {"node": "*"}}, "node_modules/hosted-git-info": {"version": "2.6.0", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/html-angular-validate": {"version": "0.2.3", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.0", "filendir": "~1.0.0", "globule": "^1.2.0", "node.extend": "^2.0.0", "string.prototype.endswith": "~0.2.0", "w3cjs": "^0.4.0", "xmlbuilder": "^9.0.4"}, "engines": {"node": ">= 5.0.0"}}, "node_modules/html-angular-validate/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/html-angular-validate/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/html-minifier": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^3.0.0", "clean-css": "^4.2.1", "commander": "^2.19.0", "he": "^1.2.0", "param-case": "^2.1.1", "relateurl": "^0.2.7", "uglify-js": "^3.5.1"}, "bin": {"html-minifier": "cli.js"}, "engines": {"node": ">=6"}}, "node_modules/html-minifier/node_modules/commander": {"version": "2.20.0", "dev": true, "license": "MIT"}, "node_modules/htmlparser2": {"version": "3.8.3", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "1", "domhandler": "2.3", "domutils": "1.5", "entities": "1.0", "readable-stream": "1.1"}}, "node_modules/htmlparser2/node_modules/isarray": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/htmlparser2/node_modules/readable-stream": {"version": "1.1.14", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/htmlparser2/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "1.6.3", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-parser-js": {"version": "0.4.10", "dev": true, "license": "MIT"}, "node_modules/http-proxy": {"version": "1.18.0", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/http-proxy-agent": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "4", "debug": "3.1.0"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/http-proxy-agent/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/http-signature": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/https-browserify": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/https-proxy-agent": {"version": "2.2.4", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/https-proxy-agent/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/iconv-lite": {"version": "0.4.24", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.1.13", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/iltorb": {"version": "2.4.5", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "nan": "^2.14.0", "npmlog": "^4.1.2", "prebuild-install": "^5.3.3", "which-pm-runs": "^1.0.0"}}, "node_modules/iltorb/node_modules/nan": {"version": "2.14.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/image-size": {"version": "0.5.5", "dev": true, "license": "MIT", "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/indent-string": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"repeating": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/indexof": {"version": "0.0.1", "dev": true}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/ini": {"version": "1.3.5", "license": "ISC", "engines": {"node": "*"}}, "node_modules/interpret": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/invert-kv": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ip": {"version": "1.1.5", "dev": true, "license": "MIT"}, "node_modules/irregular-plurals": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is": {"version": "3.3.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/is-absolute": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-accessor-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-binary-path": {"version": "1.0.1", "license": "MIT", "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-buffer": {"version": "1.1.6", "license": "MIT"}, "node_modules/is-builtin-module": {"version": "1.0.0", "license": "MIT", "dependencies": {"builtin-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-data-descriptor": {"version": "0.1.4", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-descriptor/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finite": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "1.0.0", "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-negated-glob": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-number-like": {"version": "1.0.8", "dev": true, "license": "ISC", "dependencies": {"lodash.isfinite": "^3.3.2"}}, "node_modules/is-plain-object": {"version": "2.0.4", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-relative": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-unc-path": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-typedarray": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/is-unc-path": {"version": "1.0.0", "license": "MIT", "dependencies": {"unc-path-regex": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-utf8": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-valid-glob": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-windows": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isbinaryfile": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"buffer-alloc": "^1.2.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isstream": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/jasmine-core": {"version": "3.5.0", "dev": true, "license": "MIT"}, "node_modules/jit-grunt": {"version": "0.10.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}, "peerDependencies": {"grunt": ">=0.4.0"}}, "node_modules/js-yaml": {"version": "3.5.5", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/jshint": {"version": "2.10.2", "dev": true, "license": "(MIT AND JSON)", "dependencies": {"cli": "~1.0.0", "console-browserify": "1.1.x", "exit": "0.1.x", "htmlparser2": "3.8.x", "lodash": "~4.17.11", "minimatch": "~3.0.2", "shelljs": "0.3.x", "strip-json-comments": "1.0.x"}, "bin": {"jshint": "bin/jshint"}}, "node_modules/jshint-stylish": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"beeper": "^1.1.0", "chalk": "^1.0.0", "log-symbols": "^1.0.0", "plur": "^2.1.0", "string-length": "^1.0.0", "text-table": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jshint/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/jshint/node_modules/strip-json-comments": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"strip-json-comments": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/json-schema": {"version": "0.2.3", "dev": true, "optional": true}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsprim": {"version": "1.4.1", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "optional": true, "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "node_modules/just-debounce": {"version": "1.0.0", "license": "MIT"}, "node_modules/karma": {"version": "4.4.1", "dev": true, "license": "MIT", "dependencies": {"bluebird": "^3.3.0", "body-parser": "^1.16.1", "braces": "^3.0.2", "chokidar": "^3.0.0", "colors": "^1.1.0", "connect": "^3.6.0", "di": "^0.0.1", "dom-serialize": "^2.2.0", "flatted": "^2.0.0", "glob": "^7.1.1", "graceful-fs": "^4.1.2", "http-proxy": "^1.13.0", "isbinaryfile": "^3.0.0", "lodash": "^4.17.14", "log4js": "^4.0.0", "mime": "^2.3.1", "minimatch": "^3.0.2", "optimist": "^0.6.1", "qjobs": "^1.1.4", "range-parser": "^1.2.0", "rimraf": "^2.6.0", "safe-buffer": "^5.0.1", "socket.io": "2.1.1", "source-map": "^0.6.1", "tmp": "0.0.33", "useragent": "2.3.0"}, "bin": {"karma": "bin/karma"}, "engines": {"node": ">= 8"}}, "node_modules/karma-chrome-launcher": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"which": "^1.2.1"}}, "node_modules/karma-jasmine": {"version": "3.1.1", "dev": true, "license": "MIT", "dependencies": {"jasmine-core": "^3.5.0"}, "engines": {"node": ">= 8"}, "peerDependencies": {"karma": "*"}}, "node_modules/karma-junit-reporter": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"path-is-absolute": "^1.0.0", "xmlbuilder": "12.0.0"}, "engines": {"node": ">= 8"}, "peerDependencies": {"karma": ">=0.9"}}, "node_modules/karma-junit-reporter/node_modules/xmlbuilder": {"version": "12.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/karma-mocha-reporter": {"version": "2.2.5", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.1.0", "log-symbols": "^2.1.0", "strip-ansi": "^4.0.0"}, "peerDependencies": {"karma": ">=0.13"}}, "node_modules/karma-mocha-reporter/node_modules/ansi-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/karma-mocha-reporter/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/karma-mocha-reporter/node_modules/chalk": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/karma-mocha-reporter/node_modules/log-symbols": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/karma-mocha-reporter/node_modules/strip-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/karma-mocha-reporter/node_modules/supports-color": {"version": "5.4.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/karma-ng-html2js-preprocessor": {"version": "1.0.0", "dev": true, "license": "MIT", "peerDependencies": {"karma": ">=0.9"}}, "node_modules/karma/node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/karma/node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/karma/node_modules/glob": {"version": "7.1.6", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/karma/node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/karma/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/karma/node_modules/mime": {"version": "2.4.4", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/karma/node_modules/rimraf": {"version": "2.7.1", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/karma/node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/klaw": {"version": "1.3.1", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.9"}}, "node_modules/last-run": {"version": "1.1.1", "license": "MIT", "dependencies": {"default-resolution": "^2.0.0", "es6-weak-map": "^2.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/lazystream": {"version": "1.0.0", "license": "MIT", "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/lcid": {"version": "1.0.0", "license": "MIT", "dependencies": {"invert-kv": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/lead": {"version": "1.0.0", "license": "MIT", "dependencies": {"flush-write-stream": "^1.0.2"}, "engines": {"node": ">= 0.10"}}, "node_modules/less": {"version": "3.10.3", "dev": true, "license": "Apache-2.0", "dependencies": {"clone": "^2.1.2"}, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=6"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}}, "node_modules/levn": {"version": "0.3.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/liftoff": {"version": "3.1.0", "license": "MIT", "dependencies": {"extend": "^3.0.0", "findup-sync": "^3.0.0", "fined": "^1.0.1", "flagged-respawn": "^1.0.0", "is-plain-object": "^2.0.4", "object.map": "^1.0.0", "rechoir": "^0.6.2", "resolve": "^1.1.7"}, "engines": {"node": ">= 0.8"}}, "node_modules/liftoff/node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/braces": {"version": "2.3.2", "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/is-accessor-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/is-data-descriptor": {"version": "0.1.4", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/expand-brackets/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/fill-range": {"version": "4.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/findup-sync": {"version": "3.0.0", "license": "MIT", "dependencies": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/liftoff/node_modules/is-accessor-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/is-data-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/is-descriptor": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/is-glob": {"version": "4.0.1", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftoff/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/livereload-js": {"version": "2.4.0", "dev": true, "license": "MIT"}, "node_modules/load-grunt-config": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"cson": "5.1.0", "glob": "7.1.4", "jit-grunt": "0.10.0", "js-yaml": "3.13.1", "load-grunt-tasks": "5.1.0", "lodash": "4.17.15"}, "engines": {"node": ">=8"}}, "node_modules/load-grunt-config/node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/load-grunt-config/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/load-grunt-config/node_modules/js-yaml": {"version": "3.13.1", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/load-grunt-config/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/load-grunt-tasks": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"arrify": "^2.0.1", "multimatch": "^4.0.0", "pkg-up": "^3.1.0", "resolve-pkg": "^2.0.0"}, "engines": {"node": ">=8"}, "peerDependencies": {"grunt": ">=1"}}, "node_modules/load-json-file": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/locate-path": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/locate-path/node_modules/path-exists": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/lodash": {"version": "4.17.10", "dev": true, "license": "MIT"}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "dev": true, "license": "MIT"}, "node_modules/lodash.isfinite": {"version": "3.3.2", "dev": true, "license": "MIT"}, "node_modules/log-symbols": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/log4js": {"version": "4.5.1", "dev": true, "license": "Apache-2.0", "dependencies": {"date-format": "^2.0.0", "debug": "^4.1.1", "flatted": "^2.0.0", "rfdc": "^1.1.4", "streamroller": "^1.0.6"}, "engines": {"node": ">=6.0"}}, "node_modules/log4js/node_modules/debug": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/log4js/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/loud-rejection": {"version": "1.6.0", "dev": true, "license": "MIT", "dependencies": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/lower-case": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "4.1.5", "dev": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/make-iterator": {"version": "1.0.1", "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/make-iterator/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-cache": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-obj": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep": {"version": "2.0.0", "license": "MIT", "dependencies": {"findup-sync": "^2.0.0", "micromatch": "^3.0.4", "resolve": "^1.4.0", "stack-trace": "0.0.10"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/matchdep/node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/braces": {"version": "2.3.2", "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/is-accessor-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/is-data-descriptor": {"version": "0.1.4", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.6", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/expand-brackets/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/fill-range": {"version": "4.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/findup-sync": {"version": "2.0.0", "license": "MIT", "dependencies": {"detect-file": "^1.0.0", "is-glob": "^3.1.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/matchdep/node_modules/is-accessor-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/is-data-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/is-descriptor": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/is-glob": {"version": "3.1.0", "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/matchdep/node_modules/resolve": {"version": "1.12.0", "license": "MIT", "dependencies": {"path-parse": "^1.0.6"}}, "node_modules/maxmin": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^1.0.0", "figures": "^1.0.1", "gzip-size": "^3.0.0", "pretty-bytes": "^3.0.0"}, "engines": {"node": ">=0.12"}}, "node_modules/maxmin/node_modules/pretty-bytes": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/media-typer": {"version": "0.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/meow": {"version": "3.7.0", "dev": true, "license": "MIT", "dependencies": {"camelcase-keys": "^2.0.0", "decamelize": "^1.1.2", "loud-rejection": "^1.0.0", "map-obj": "^1.0.1", "minimist": "^1.1.3", "normalize-package-data": "^2.3.4", "object-assign": "^4.0.1", "read-pkg-up": "^1.0.1", "redent": "^1.0.0", "trim-newlines": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/methods": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime": {"version": "1.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.40.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.24", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.40.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimeparse": {"version": "0.1.4", "dev": true}, "node_modules/mimic-response": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "3.0.4", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.0", "dev": true, "license": "MIT"}, "node_modules/mixin-deep": {"version": "1.3.2", "license": "MIT", "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mixin-deep/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.1", "dev": true, "license": "MIT", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp/node_modules/minimist": {"version": "0.0.8", "dev": true, "license": "MIT"}, "node_modules/morgan": {"version": "1.9.1", "dev": true, "license": "MIT", "dependencies": {"basic-auth": "~2.0.0", "debug": "2.6.9", "depd": "~1.1.2", "on-finished": "~2.3.0", "on-headers": "~1.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/multimatch": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/mute-stdout": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/nan": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/nanomatch": {"version": "1.2.13", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/napi-build-utils": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/negotiator": {"version": "0.6.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/netmask": {"version": "1.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/next-tick": {"version": "1.0.0", "license": "MIT"}, "node_modules/ng-annotate": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"acorn": "~2.6.4", "alter": "~0.2.0", "convert-source-map": "~1.1.2", "optimist": "~0.6.1", "ordered-ast-traverse": "~1.1.1", "simple-fmt": "~0.1.0", "simple-is": "~0.2.0", "source-map": "~0.5.3", "stable": "~0.1.5", "stringmap": "~0.2.2", "stringset": "~0.2.1", "tryor": "~0.1.2"}, "bin": {"ng-annotate": "build/es5/ng-annotate"}}, "node_modules/ng-annotate/node_modules/source-map": {"version": "0.5.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/no-case": {"version": "2.3.2", "dev": true, "license": "MIT", "dependencies": {"lower-case": "^1.1.1"}}, "node_modules/node-abi": {"version": "2.15.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"semver": "^5.4.1"}}, "node_modules/node-http2": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"assert": "1.4.1", "events": "1.1.1", "https-browserify": "0.0.1", "setimmediate": "^1.0.5", "stream-browserify": "2.0.1", "timers-browserify": "2.0.2", "url": "^0.11.0", "websocket-stream": "^5.0.1"}, "engines": {"node": ">=0.12.0"}}, "node_modules/node.extend": {"version": "2.0.2", "dev": true, "license": "(MIT OR GPL-2.0)", "dependencies": {"has": "^1.0.3", "is": "^3.2.1"}, "engines": {"node": ">=0.4.0"}}, "node_modules/noop-logger": {"version": "0.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/nopt": {"version": "3.0.6", "dev": true, "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/normalize-package-data": {"version": "2.4.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-path": {"version": "2.1.1", "license": "MIT", "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/now-and-later": {"version": "2.0.1", "license": "MIT", "dependencies": {"once": "^1.3.2"}, "engines": {"node": ">= 0.10"}}, "node_modules/npmlog": {"version": "4.1.2", "dev": true, "license": "ISC", "optional": true, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/nth-check": {"version": "1.0.1", "dev": true, "license": "BSD", "dependencies": {"boolbase": "~1.0.0"}}, "node_modules/number-is-nan": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/oauth-sign": {"version": "0.9.0", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-component": {"version": "0.0.3", "dev": true}, "node_modules/object-copy": {"version": "0.1.0", "license": "MIT", "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-keys": {"version": "1.0.11", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object-visit": {"version": "1.0.1", "license": "MIT", "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-visit/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.0", "license": "MIT", "dependencies": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.defaults": {"version": "1.1.0", "license": "MIT", "dependencies": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults/node_modules/array-slice": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults/node_modules/for-own": {"version": "1.0.0", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object.map": {"version": "1.0.1", "license": "MIT", "dependencies": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.map/node_modules/for-own": {"version": "1.0.0", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object.reduce": {"version": "1.0.1", "license": "MIT", "dependencies": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.reduce/node_modules/for-own": {"version": "1.0.0", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/opn": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"is-wsl": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/optimist": {"version": "0.6.1", "dev": true, "license": "MIT/X11", "dependencies": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}}, "node_modules/optimist/node_modules/minimist": {"version": "0.0.10", "dev": true, "license": "MIT"}, "node_modules/optionator": {"version": "0.8.2", "dev": true, "license": "MIT", "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.4", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "wordwrap": "~1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/optionator/node_modules/wordwrap": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/options": {"version": "0.0.6", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/ordered-ast-traverse": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"ordered-esprima-props": "~1.1.0"}}, "node_modules/ordered-esprima-props": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/ordered-read-streams": {"version": "1.0.1", "license": "MIT", "dependencies": {"readable-stream": "^2.0.1"}}, "node_modules/os-locale": {"version": "1.4.0", "license": "MIT", "dependencies": {"lcid": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/p-limit": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pac-proxy-agent": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^4.2.0", "debug": "^3.1.0", "get-uri": "^2.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "pac-resolver": "^3.0.0", "raw-body": "^2.2.0", "socks-proxy-agent": "^3.0.0"}}, "node_modules/pac-proxy-agent/node_modules/bytes": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pac-proxy-agent/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/pac-proxy-agent/node_modules/http-errors": {"version": "1.7.3", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/pac-proxy-agent/node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/pac-proxy-agent/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/pac-proxy-agent/node_modules/raw-body": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.3", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/pac-proxy-agent/node_modules/setprototypeof": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/pac-resolver": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"co": "^4.6.0", "degenerator": "^1.0.4", "ip": "^1.1.5", "netmask": "^1.0.6", "thunkify": "^2.1.2"}}, "node_modules/param-case": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"no-case": "^2.2.0"}}, "node_modules/parse-filepath": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/parse-json": {"version": "2.2.0", "license": "MIT", "dependencies": {"error-ex": "^1.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parse-node-version": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/parse-passwd": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/parseqs": {"version": "0.0.5", "dev": true, "license": "MIT", "dependencies": {"better-assert": "~1.0.0"}}, "node_modules/parseuri": {"version": "0.0.5", "dev": true, "license": "MIT", "dependencies": {"better-assert": "~1.0.0"}}, "node_modules/parseurl": {"version": "1.3.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascalcase": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-dirname": {"version": "1.0.2", "license": "MIT"}, "node_modules/path-exists": {"version": "2.1.0", "license": "MIT", "dependencies": {"pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-parse": {"version": "1.0.6", "license": "MIT"}, "node_modules/path-root": {"version": "0.1.1", "license": "MIT", "dependencies": {"path-root-regex": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-root-regex": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-type": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/picomatch": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie": {"version": "2.0.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "license": "MIT", "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pkg-up": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up/node_modules/find-up": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/plur": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"irregular-plurals": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/portscanner": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.0", "is-number-like": "^1.0.3"}, "engines": {"node": ">=0.4", "npm": ">=1.0.0"}}, "node_modules/portscanner/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/portscanner/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/posix-character-classes": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/prebuild-install": {"version": "5.3.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "napi-build-utils": "^1.0.1", "node-abi": "^2.7.0", "noop-logger": "^0.1.1", "npmlog": "^4.0.1", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^3.0.3", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0"}, "bin": {"prebuild-install": "bin.js"}, "engines": {"node": ">=6"}}, "node_modules/prebuild-install/node_modules/pump": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/prelude-ls": {"version": "1.1.2", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/pretty-bytes": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/pretty-hrtime": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/process-nextick-args": {"version": "2.0.0", "license": "MIT"}, "node_modules/progress": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "7.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"asap": "~2.0.3"}}, "node_modules/proxy-agent": {"version": "2.3.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^4.2.0", "debug": "^3.1.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "lru-cache": "^4.1.2", "pac-proxy-agent": "^2.0.1", "proxy-from-env": "^1.0.0", "socks-proxy-agent": "^3.0.0"}}, "node_modules/proxy-agent/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/proxy-agent/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/proxy-from-env": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/prr": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/pseudomap": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/psl": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/pump": {"version": "2.0.1", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pumpify": {"version": "1.5.1", "license": "MIT", "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "node_modules/punycode": {"version": "2.1.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/puppeteer": {"version": "2.1.1", "dev": true, "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"@types/mime-types": "^2.1.0", "debug": "^4.1.0", "extract-zip": "^1.6.6", "https-proxy-agent": "^4.0.0", "mime": "^2.0.3", "mime-types": "^2.1.25", "progress": "^2.0.1", "proxy-from-env": "^1.0.0", "rimraf": "^2.6.1", "ws": "^6.1.0"}, "engines": {"node": ">=8.16.0"}}, "node_modules/puppeteer/node_modules/agent-base": {"version": "5.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/puppeteer/node_modules/debug": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/puppeteer/node_modules/glob": {"version": "7.1.6", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/puppeteer/node_modules/https-proxy-agent": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "5", "debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/puppeteer/node_modules/mime": {"version": "2.4.4", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/puppeteer/node_modules/mime-db": {"version": "1.43.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/puppeteer/node_modules/mime-types": {"version": "2.1.26", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.43.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/puppeteer/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/puppeteer/node_modules/rimraf": {"version": "2.7.1", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/puppeteer/node_modules/ws": {"version": "6.2.1", "dev": true, "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "node_modules/q": {"version": "0.9.3", "dev": true, "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/q-io": {"version": "1.13.6", "dev": true, "license": "MIT", "dependencies": {"es6-set": "^0.1.1", "mime": "^1.2.11", "mimeparse": "^0.1.4", "q": "^1.0.1", "qs": "^1.2.1", "url2": "^0.0.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/q-io/node_modules/q": {"version": "1.5.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/q-io/node_modules/qs": {"version": "1.2.2", "dev": true}, "node_modules/qjobs": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.9"}}, "node_modules/qs": {"version": "6.5.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/querystring": {"version": "0.2.0", "dev": true, "engines": {"node": ">=0.4.x"}}, "node_modules/range-parser": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "1.1.7", "dev": true, "license": "MIT", "dependencies": {"bytes": "1", "string_decoder": "0.10"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/raw-body/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "license": "MIT"}, "node_modules/rc": {"version": "1.2.8", "dev": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "optional": true, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/read-pkg": {"version": "1.1.0", "license": "MIT", "dependencies": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up": {"version": "1.0.1", "license": "MIT", "dependencies": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "2.3.6", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readdirp": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.0.7"}, "engines": {"node": ">=8.10.0"}}, "node_modules/rechoir": {"version": "0.6.2", "dependencies": {"resolve": "^1.1.6"}, "engines": {"node": ">= 0.10"}}, "node_modules/redent": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regex-not": {"version": "1.0.2", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/relateurl": {"version": "0.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/remove-bom-buffer": {"version": "3.0.0", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5", "is-utf8": "^0.2.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/remove-bom-stream": {"version": "1.2.0", "license": "MIT", "dependencies": {"remove-bom-buffer": "^3.0.0", "safe-buffer": "^5.1.0", "through2": "^2.0.3"}, "engines": {"node": ">= 0.10"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "license": "ISC"}, "node_modules/repeat-element": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/repeating": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-finite": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/replace-ext": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/replace-homedir": {"version": "1.0.0", "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1", "is-absolute": "^1.0.0", "remove-trailing-separator": "^1.1.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/request": {"version": "2.88.0", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 4"}}, "node_modules/request/node_modules/extend": {"version": "3.0.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "1.0.1", "license": "ISC"}, "node_modules/requirefresh": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"editions": "^2.2.0"}, "engines": {"node": ">=0.12"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/requirefresh/node_modules/editions": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"errlop": "^2.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/requirefresh/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/requires-port": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.1.7", "license": "MIT"}, "node_modules/resolve-dir": {"version": "1.0.1", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-from": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-options": {"version": "1.1.0", "license": "MIT", "dependencies": {"value-or-function": "^3.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/resolve-pkg": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-url": {"version": "0.2.1", "license": "MIT"}, "node_modules/ret": {"version": "0.1.15", "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/rfdc": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/glob": {"version": "7.1.6", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rsvp": {"version": "3.6.2", "dev": true, "license": "MIT", "engines": {"node": "0.12.* || 4.* || 6.* || >= 7.*"}}, "node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/safe-json-parse": {"version": "1.0.1", "dev": true}, "node_modules/safe-regex": {"version": "1.1.0", "license": "MIT", "dependencies": {"ret": "~0.1.10"}}, "node_modules/safefs": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"editions": "^2.2.0", "graceful-fs": "^4.2.3"}, "engines": {"node": ">=0.12"}, "funding": {"type": "cooperative", "url": "https://bevry.me/fund"}}, "node_modules/safefs/node_modules/editions": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"errlop": "^2.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/safefs/node_modules/graceful-fs": {"version": "4.2.3", "dev": true, "license": "ISC"}, "node_modules/safefs/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/semver": {"version": "5.5.0", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/semver-greatest-satisfied-range": {"version": "1.1.0", "license": "MIT", "dependencies": {"sver-compat": "^1.5.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/send": {"version": "0.17.1", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/http-errors": {"version": "1.7.3", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/send/node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "dev": true, "license": "MIT"}, "node_modules/send/node_modules/setprototypeof": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/serve-index": {"version": "1.9.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-static": {"version": "1.14.1", "dev": true, "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "license": "ISC"}, "node_modules/set-value": {"version": "2.0.1", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "dev": true, "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.1.0", "dev": true, "license": "ISC"}, "node_modules/shelljs": {"version": "0.3.0", "dev": true, "license": "BSD*", "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/signal-exit": {"version": "3.0.2", "dev": true, "license": "ISC"}, "node_modules/simple-concat": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/simple-fmt": {"version": "0.1.0", "dev": true, "license": "MIT"}, "node_modules/simple-get": {"version": "3.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"decompress-response": "^4.2.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/simple-is": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/smart-buffer": {"version": "1.1.15", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10.15", "npm": ">= 1.3.5"}}, "node_modules/snapdragon": {"version": "0.8.2", "license": "MIT", "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "license": "MIT", "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/is-accessor-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/is-data-descriptor": {"version": "1.0.0", "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/is-descriptor": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/kind-of": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/socket.io": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"debug": "~3.1.0", "engine.io": "~3.2.0", "has-binary2": "~1.0.2", "socket.io-adapter": "~1.1.0", "socket.io-client": "2.1.1", "socket.io-parser": "~3.2.0"}}, "node_modules/socket.io-adapter": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/socket.io-client": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"backo2": "1.0.2", "base64-arraybuffer": "0.1.5", "component-bind": "1.0.0", "component-emitter": "1.2.1", "debug": "~3.1.0", "engine.io-client": "~3.2.0", "has-binary2": "~1.0.2", "has-cors": "1.1.0", "indexof": "0.0.1", "object-component": "0.0.3", "parseqs": "0.0.5", "parseuri": "0.0.5", "socket.io-parser": "~3.2.0", "to-array": "0.1.4"}}, "node_modules/socket.io-client/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/socket.io-parser": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"component-emitter": "1.2.1", "debug": "~3.1.0", "isarray": "2.0.1"}}, "node_modules/socket.io-parser/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/socket.io-parser/node_modules/isarray": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/socket.io/node_modules/debug": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/socks": {"version": "1.1.10", "dev": true, "license": "MIT", "dependencies": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "engines": {"node": ">= 0.10.0", "npm": ">= 1.3.5"}}, "node_modules/socks-proxy-agent": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^4.1.0", "socks": "^1.1.10"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.2", "license": "MIT", "dependencies": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.2.10", "dev": true, "dependencies": {"source-map": "0.1.32"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.1.32", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/source-map-url": {"version": "0.4.0", "license": "MIT"}, "node_modules/sparkles": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/spdx-correct": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.1.0", "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.0", "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.0", "license": "CC0-1.0"}, "node_modules/split-string": {"version": "3.1.0", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sshpk": {"version": "1.16.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stable": {"version": "0.1.8", "dev": true, "license": "MIT"}, "node_modules/stack-trace": {"version": "0.0.10", "license": "MIT", "engines": {"node": "*"}}, "node_modules/static-extend": {"version": "0.1.2", "license": "MIT", "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "1.5.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/stream-browserify": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "node_modules/stream-buffers": {"version": "2.2.0", "dev": true, "license": "Unlicense", "engines": {"node": ">= 0.10.0"}}, "node_modules/stream-exhaust": {"version": "1.0.2", "license": "MIT"}, "node_modules/stream-shift": {"version": "1.0.0", "license": "MIT"}, "node_modules/streamqueue": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"isstream": "^0.1.2", "readable-stream": "^2.3.3"}, "engines": {"node": ">=6.9.5"}}, "node_modules/streamroller": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.2", "date-format": "^2.0.0", "debug": "^3.2.6", "fs-extra": "^7.0.1", "lodash": "^4.17.14"}, "engines": {"node": ">=6.0"}}, "node_modules/streamroller/node_modules/async": {"version": "2.6.3", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/streamroller/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/streamroller/node_modules/fs-extra": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/streamroller/node_modules/lodash": {"version": "4.17.15", "dev": true, "license": "MIT"}, "node_modules/streamroller/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string-length": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/string-template": {"version": "0.2.1", "dev": true}, "node_modules/string-width": {"version": "1.0.2", "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/string.prototype.endswith": {"version": "0.2.0", "dev": true}, "node_modules/stringmap": {"version": "0.2.2", "dev": true, "license": "MIT"}, "node_modules/stringset": {"version": "0.2.1", "dev": true, "license": "MIT"}, "node_modules/strip-ansi": {"version": "3.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-bom": {"version": "2.0.0", "license": "MIT", "dependencies": {"is-utf8": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-indent": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"get-stdin": "^4.0.1"}, "bin": {"strip-indent": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/superagent": {"version": "3.8.3", "dev": true, "license": "MIT", "dependencies": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}, "engines": {"node": ">= 4.0"}}, "node_modules/superagent-proxy": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.1.0", "proxy-agent": "2"}, "peerDependencies": {"superagent": ">= 0.15.4 || 1 || 2 || 3"}}, "node_modules/superagent-proxy/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/superagent-proxy/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/superagent/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/superagent/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/supports-color": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/sver-compat": {"version": "1.5.0", "license": "MIT", "dependencies": {"es6-iterator": "^2.0.1", "es6-symbol": "^3.1.1"}}, "node_modules/tar-fs": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.1", "pump": "^3.0.0", "tar-stream": "^2.0.0"}}, "node_modules/tar-fs/node_modules/bl": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"readable-stream": "^3.0.1"}}, "node_modules/tar-fs/node_modules/pump": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/tar-fs/node_modules/readable-stream": {"version": "3.6.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/tar-fs/node_modules/tar-stream": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"bl": "^3.0.0", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}}, "node_modules/tar-stream": {"version": "1.6.2", "dev": true, "license": "MIT", "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/through2": {"version": "2.0.3", "license": "MIT", "dependencies": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "node_modules/through2-filter": {"version": "3.0.0", "license": "MIT", "dependencies": {"through2": "~2.0.0", "xtend": "~4.0.0"}}, "node_modules/thunkify": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/time-stamp": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/timers-browserify": {"version": "2.0.2", "dev": true, "dependencies": {"setimmediate": "^1.0.4"}, "engines": {"node": ">=0.6.0"}}, "node_modules/tiny-lr": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"body": "^5.1.0", "debug": "^3.1.0", "faye-websocket": "~0.10.0", "livereload-js": "^2.3.0", "object-assign": "^4.1.0", "qs": "^6.4.0"}}, "node_modules/tiny-lr/node_modules/debug": {"version": "3.2.6", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/tiny-lr/node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/tinycolor": {"version": "0.0.1", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/tmp": {"version": "0.0.33", "dev": true, "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-absolute-glob": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-absolute": "^1.0.0", "is-negated-glob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-array": {"version": "0.1.4", "dev": true}, "node_modules/to-buffer": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/to-object-path": {"version": "0.3.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "license": "MIT", "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range/node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-through": {"version": "2.0.0", "license": "MIT", "dependencies": {"through2": "^2.0.3"}, "engines": {"node": ">= 0.10"}}, "node_modules/toidentifier": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tough-cookie": {"version": "2.4.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tough-cookie/node_modules/punycode": {"version": "1.4.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/traceur": {"version": "0.0.111", "dev": true, "license": "Apache-2.0", "dependencies": {"commander": "2.9.x", "glob": "5.0.x", "rsvp": "^3.0.13", "semver": "^4.3.3", "source-map-support": "~0.2.8"}, "bin": {"traceur": "traceur"}, "engines": {"node": ">=0.10"}}, "node_modules/traceur/node_modules/glob": {"version": "5.0.15", "dev": true, "license": "ISC", "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/traceur/node_modules/semver": {"version": "4.3.6", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/trim-newlines": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/tryor": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/tunnel-agent": {"version": "0.6.0", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "dev": true, "license": "Unlicense", "optional": true}, "node_modules/type": {"version": "1.0.3", "license": "ISC"}, "node_modules/type-check": {"version": "0.3.2", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-is": {"version": "1.6.18", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typechecker": {"version": "4.11.0", "dev": true, "license": "MIT", "dependencies": {"editions": "^2.2.0"}, "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/typechecker/node_modules/editions": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"errlop": "^2.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=0.8"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/typechecker/node_modules/semver": {"version": "6.3.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/typedarray": {"version": "0.0.6", "license": "MIT"}, "node_modules/uglify-js": {"version": "3.6.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"commander": "~2.20.0", "source-map": "~0.6.1"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/uglify-js/node_modules/commander": {"version": "2.20.0", "dev": true, "license": "MIT"}, "node_modules/ultron": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/unc-path-regex": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/underscore": {"version": "1.4.4", "dev": true}, "node_modules/underscore.string": {"version": "3.3.5", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}, "engines": {"node": "*"}}, "node_modules/undertaker": {"version": "1.2.1", "license": "MIT", "dependencies": {"arr-flatten": "^1.0.1", "arr-map": "^2.0.0", "bach": "^1.0.0", "collection-map": "^1.0.0", "es6-weak-map": "^2.0.1", "last-run": "^1.1.0", "object.defaults": "^1.0.0", "object.reduce": "^1.0.0", "undertaker-registry": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/undertaker-registry": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/union-value": {"version": "1.0.1", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unique-stream": {"version": "2.3.1", "license": "MIT", "dependencies": {"json-stable-stringify-without-jsonify": "^1.0.1", "through2-filter": "^3.0.0"}}, "node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unset-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "license": "MIT", "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/upath": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/upper-case": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/uri-js": {"version": "4.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true, "dependencies": {"punycode": "^2.1.0"}}, "node_modules/uri-path": {"version": "1.0.0", "dev": true, "license": "WTFPL OR MIT", "engines": {"node": ">= 0.10"}}, "node_modules/urix": {"version": "0.1.0", "license": "MIT"}, "node_modules/url": {"version": "0.11.0", "dev": true, "license": "MIT", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "node_modules/url-safe": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/url/node_modules/punycode": {"version": "1.3.2", "dev": true, "license": "MIT"}, "node_modules/url2": {"version": "0.0.0", "dev": true}, "node_modules/use": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/useragent": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"lru-cache": "4.1.x", "tmp": "0.0.x"}}, "node_modules/util": {"version": "0.10.3", "dev": true, "license": "MIT", "dependencies": {"inherits": "2.0.1"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/util/node_modules/inherits": {"version": "2.0.1", "dev": true, "license": "ISC"}, "node_modules/utils-merge": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.3.3", "dev": true, "license": "MIT", "optional": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/v8flags": {"version": "3.1.3", "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/validate-npm-package-license": {"version": "3.0.3", "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/value-or-function": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/verror": {"version": "1.10.0", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "optional": true, "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/vinyl": {"version": "2.2.0", "license": "MIT", "dependencies": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/vinyl-fs": {"version": "3.0.3", "license": "MIT", "dependencies": {"fs-mkdirp-stream": "^1.0.0", "glob-stream": "^6.1.0", "graceful-fs": "^4.0.0", "is-valid-glob": "^1.0.0", "lazystream": "^1.0.0", "lead": "^1.0.0", "object.assign": "^4.0.4", "pumpify": "^1.3.5", "readable-stream": "^2.3.3", "remove-bom-buffer": "^3.0.0", "remove-bom-stream": "^1.2.0", "resolve-options": "^1.1.0", "through2": "^2.0.0", "to-through": "^2.0.0", "value-or-function": "^3.0.0", "vinyl": "^2.0.0", "vinyl-sourcemap": "^1.1.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/vinyl-sourcemap": {"version": "1.1.0", "license": "MIT", "dependencies": {"append-buffer": "^1.0.2", "convert-source-map": "^1.5.0", "graceful-fs": "^4.1.6", "normalize-path": "^2.1.1", "now-and-later": "^2.0.0", "remove-bom-buffer": "^3.0.0", "vinyl": "^2.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/vinyl-sourcemap/node_modules/convert-source-map": {"version": "1.6.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.1"}}, "node_modules/void-elements": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/w3cjs": {"version": "0.4.0", "dev": true, "license": "Unlicense", "dependencies": {"commander": "^2.9.0", "superagent": "^3.5.2", "superagent-proxy": "^1.0.2"}, "bin": {"w3cjs": "bin/w3cjs"}, "engines": {"node": "*"}}, "node_modules/walkdir": {"version": "0.0.11", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.0"}}, "node_modules/websocket-driver": {"version": "0.7.3", "dev": true, "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.4.0 <0.4.11", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-stream": {"version": "5.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"duplexify": "^3.5.1", "inherits": "^2.0.1", "readable-stream": "^2.3.3", "safe-buffer": "^5.1.2", "ws": "^3.2.0", "xtend": "^4.0.0"}}, "node_modules/websocket-stream/node_modules/ws": {"version": "3.3.3", "dev": true, "license": "MIT", "dependencies": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}, "node_modules/which": {"version": "1.2.14", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-module": {"version": "1.0.0", "license": "ISC"}, "node_modules/which-pm-runs": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/wide-align": {"version": "1.1.3", "dev": true, "license": "ISC", "optional": true, "dependencies": {"string-width": "^1.0.2 || 2"}}, "node_modules/wordwrap": {"version": "0.0.2", "dev": true, "license": "MIT/X11", "engines": {"node": ">=0.4.0"}}, "node_modules/wrap-ansi": {"version": "2.1.0", "license": "MIT", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/ws": {"version": "0.4.32", "dev": true, "hasInstallScript": true, "dependencies": {"commander": "~2.1.0", "nan": "~1.0.0", "options": ">=0.0.5", "tinycolor": "0.x"}, "bin": {"wscat": "bin/wscat"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ws/node_modules/commander": {"version": "2.1.0", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/xmlbuilder": {"version": "9.0.7", "dev": true, "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/xmlhttprequest-ssl": {"version": "1.5.5", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/xregexp": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/xtend": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "3.2.1", "license": "ISC"}, "node_modules/yallist": {"version": "2.1.2", "dev": true, "license": "ISC"}, "node_modules/yargs-parser": {"version": "5.0.0", "license": "ISC", "dependencies": {"camelcase": "^3.0.0"}}, "node_modules/yargs-parser/node_modules/camelcase": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/yauzl": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"fd-slicer": "~1.0.1"}}, "node_modules/yeast": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/zip-stream": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"archiver-utils": "^1.3.0", "compress-commons": "^1.2.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 0.10.0"}}}, "dependencies": {"@types/mime-types": {"version": "2.1.0", "dev": true}, "@types/minimatch": {"version": "3.0.3", "dev": true}, "@types/node": {"version": "8.10.52", "dev": true}, "abbrev": {"version": "1.1.1", "dev": true}, "accepts": {"version": "1.3.7", "dev": true, "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "acorn": {"version": "2.6.4", "dev": true}, "after": {"version": "0.8.2", "dev": true}, "agent-base": {"version": "4.3.0", "dev": true, "requires": {"es6-promisify": "^5.0.0"}}, "ajv": {"version": "6.10.2", "dev": true, "optional": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "alter": {"version": "0.2.0", "dev": true, "requires": {"stable": "~0.1.3"}}, "amdefine": {"version": "1.0.1", "dev": true}, "ansi-colors": {"version": "1.1.0", "requires": {"ansi-wrap": "^0.1.0"}}, "ansi-gray": {"version": "0.1.1", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-regex": {"version": "2.1.1"}, "ansi-styles": {"version": "2.2.1", "dev": true}, "ansi-to-html": {"version": "0.1.1", "dev": true}, "ansi-wrap": {"version": "0.1.0"}, "anymatch": {"version": "3.1.1", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "dependencies": {"normalize-path": {"version": "3.0.0", "dev": true}}}, "append-buffer": {"version": "1.0.2", "requires": {"buffer-equal": "^1.0.0"}}, "applause": {"version": "1.2.2", "dev": true, "requires": {"cson-parser": "^1.1.0", "js-yaml": "^3.3.0", "lodash": "^3.10.0"}, "dependencies": {"lodash": {"version": "3.10.1", "dev": true}}}, "aproba": {"version": "1.2.0", "dev": true, "optional": true}, "archiver": {"version": "1.3.0", "dev": true, "requires": {"archiver-utils": "^1.3.0", "async": "^2.0.0", "buffer-crc32": "^0.2.1", "glob": "^7.0.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0", "tar-stream": "^1.5.0", "walkdir": "^0.0.11", "zip-stream": "^1.1.0"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}, "dependencies": {"lodash": {"version": "4.17.15", "dev": true}}}}}, "archiver-utils": {"version": "1.3.0", "dev": true, "requires": {"glob": "^7.0.0", "graceful-fs": "^4.1.0", "lazystream": "^1.0.0", "lodash": "^4.8.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}}, "archy": {"version": "1.0.0"}, "are-we-there-yet": {"version": "1.1.5", "dev": true, "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "argparse": {"version": "1.0.10", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "arr-filter": {"version": "1.1.2", "requires": {"make-iterator": "^1.0.0"}}, "arr-flatten": {"version": "1.1.0"}, "arr-map": {"version": "2.0.2", "requires": {"make-iterator": "^1.0.0"}}, "arr-union": {"version": "3.1.0"}, "array-differ": {"version": "3.0.0", "dev": true}, "array-each": {"version": "1.0.1"}, "array-find-index": {"version": "1.0.2", "dev": true}, "array-initial": {"version": "1.1.0", "requires": {"array-slice": "^1.0.0", "is-number": "^4.0.0"}, "dependencies": {"array-slice": {"version": "1.1.0"}, "is-number": {"version": "4.0.0"}}}, "array-last": {"version": "1.3.0", "requires": {"is-number": "^4.0.0"}, "dependencies": {"is-number": {"version": "4.0.0"}}}, "array-sort": {"version": "1.0.0", "requires": {"default-compare": "^1.0.0", "get-value": "^2.0.6", "kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0"}}}, "array-union": {"version": "2.1.0", "dev": true}, "arraybuffer.slice": {"version": "0.0.7", "dev": true}, "arrify": {"version": "2.0.1", "dev": true}, "asap": {"version": "2.0.6", "dev": true, "optional": true}, "asn1": {"version": "0.2.4", "dev": true, "optional": true, "requires": {"safer-buffer": "~2.1.0"}}, "assert": {"version": "1.4.1", "dev": true, "requires": {"util": "0.10.3"}}, "assert-plus": {"version": "1.0.0", "dev": true, "optional": true}, "assign-symbols": {"version": "1.0.0"}, "ast-types": {"version": "0.13.2", "dev": true}, "async": {"version": "1.5.2", "dev": true}, "async-done": {"version": "1.3.2", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.2", "process-nextick-args": "^2.0.0", "stream-exhaust": "^1.0.1"}}, "async-each": {"version": "1.0.1"}, "async-limiter": {"version": "1.0.1", "dev": true}, "async-settle": {"version": "1.0.0", "requires": {"async-done": "^1.2.2"}}, "asynckit": {"version": "0.4.0", "dev": true}, "atob": {"version": "2.1.2"}, "aws-sign2": {"version": "0.7.0", "dev": true, "optional": true}, "aws4": {"version": "1.8.0", "dev": true, "optional": true}, "bach": {"version": "1.2.0", "requires": {"arr-filter": "^1.1.1", "arr-flatten": "^1.0.1", "arr-map": "^2.0.0", "array-each": "^1.0.0", "array-initial": "^1.0.0", "array-last": "^1.1.1", "async-done": "^1.2.2", "async-settle": "^1.0.0", "now-and-later": "^2.0.0"}}, "backo2": {"version": "1.0.2", "dev": true}, "balanced-match": {"version": "1.0.0"}, "base": {"version": "0.11.2", "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "isobject": {"version": "3.0.1"}, "kind-of": {"version": "6.0.2"}}}, "base64-arraybuffer": {"version": "0.1.5", "dev": true}, "base64-js": {"version": "1.3.1", "dev": true}, "base64id": {"version": "1.0.0", "dev": true}, "basic-auth": {"version": "2.0.1", "dev": true, "requires": {"safe-buffer": "5.1.2"}}, "batch": {"version": "0.6.1", "dev": true}, "bcrypt-pbkdf": {"version": "1.0.2", "dev": true, "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "beeper": {"version": "1.1.1", "dev": true}, "better-assert": {"version": "1.0.2", "dev": true, "requires": {"callsite": "1.0.0"}}, "binary-extensions": {"version": "1.11.0"}, "bl": {"version": "1.2.2", "dev": true, "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "blob": {"version": "0.0.5", "dev": true}, "bluebird": {"version": "3.7.2", "dev": true}, "body": {"version": "5.1.0", "dev": true, "requires": {"continuable-cache": "^0.3.1", "error": "^7.0.0", "raw-body": "~1.1.0", "safe-json-parse": "~1.0.1"}}, "body-parser": {"version": "1.19.0", "dev": true, "requires": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "dependencies": {"bytes": {"version": "3.1.0", "dev": true}, "http-errors": {"version": "1.7.2", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "qs": {"version": "6.7.0", "dev": true}, "raw-body": {"version": "2.4.0", "dev": true, "requires": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "setprototypeof": {"version": "1.1.1", "dev": true}}}, "boolbase": {"version": "1.0.0", "dev": true}, "bower": {"version": "1.8.8", "dev": true}, "brace-expansion": {"version": "1.1.11", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "buffer": {"version": "5.4.3", "dev": true, "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}}, "buffer-alloc": {"version": "1.2.0", "dev": true, "requires": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "buffer-alloc-unsafe": {"version": "1.1.0", "dev": true}, "buffer-crc32": {"version": "0.2.13", "dev": true}, "buffer-equal": {"version": "1.0.0"}, "buffer-fill": {"version": "1.0.0", "dev": true}, "buffer-from": {"version": "1.0.0"}, "builtin-modules": {"version": "1.1.1"}, "bytes": {"version": "1.0.0", "dev": true}, "cache-base": {"version": "1.0.1", "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "dependencies": {"isobject": {"version": "3.0.1"}}}, "callsite": {"version": "1.0.0", "dev": true}, "camel-case": {"version": "3.0.0", "dev": true, "requires": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "camelcase": {"version": "2.1.1", "dev": true}, "camelcase-keys": {"version": "2.1.0", "dev": true, "requires": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}}, "caseless": {"version": "0.12.0", "dev": true, "optional": true}, "chalk": {"version": "1.1.3", "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "cheerio": {"version": "0.12.4", "dev": true, "requires": {"cheerio-select": "*", "entities": "0.x", "htmlparser2": "3.1.4", "underscore": "~1.4"}, "dependencies": {"domhandler": {"version": "2.0.3", "dev": true, "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.1.6", "dev": true, "requires": {"domelementtype": "1"}}, "entities": {"version": "0.5.0", "dev": true}, "htmlparser2": {"version": "3.1.4", "dev": true, "requires": {"domelementtype": "1", "domhandler": "2.0", "domutils": "1.1", "readable-stream": "1.0"}}, "isarray": {"version": "0.0.1", "dev": true}, "readable-stream": {"version": "1.0.34", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "dev": true}}}, "cheerio-select": {"version": "0.0.3", "dev": true, "requires": {"CSSselect": "0.x"}}, "chokidar": {"version": "3.3.1", "dev": true, "requires": {"anymatch": "~3.1.1", "braces": "~3.0.2", "fsevents": "~2.1.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.3.0"}, "dependencies": {"binary-extensions": {"version": "2.0.0", "dev": true}, "braces": {"version": "3.0.2", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "fill-range": {"version": "7.0.1", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "is-binary-path": {"version": "2.1.0", "dev": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-glob": {"version": "4.0.1", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "dev": true}, "normalize-path": {"version": "3.0.0", "dev": true}, "to-regex-range": {"version": "5.0.1", "dev": true, "requires": {"is-number": "^7.0.0"}}}}, "chownr": {"version": "1.1.4", "dev": true, "optional": true}, "class-utils": {"version": "0.3.6", "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}, "isobject": {"version": "3.0.1"}}}, "clean-css": {"version": "4.2.1", "dev": true, "requires": {"source-map": "~0.6.0"}}, "cli": {"version": "1.0.1", "dev": true, "requires": {"exit": "0.1.2", "glob": "^7.1.1"}, "dependencies": {"glob": {"version": "7.1.4", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "clone": {"version": "2.1.2"}, "clone-buffer": {"version": "1.0.0"}, "clone-stats": {"version": "1.0.0"}, "cloneable-readable": {"version": "1.1.3", "requires": {"inherits": "^2.0.1", "process-nextick-args": "^2.0.0", "readable-stream": "^2.3.5"}}, "co": {"version": "4.6.0", "dev": true}, "code-point-at": {"version": "1.1.0"}, "coffee-script": {"version": "1.10.0", "dev": true}, "collection-map": {"version": "1.0.0", "requires": {"arr-map": "^2.0.2", "for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "dependencies": {"for-own": {"version": "1.0.0", "requires": {"for-in": "^1.0.1"}}}}, "collection-visit": {"version": "1.0.0", "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.1", "dev": true, "requires": {"color-name": "^1.1.1"}}, "color-name": {"version": "1.1.3", "dev": true}, "color-support": {"version": "1.1.3"}, "colors": {"version": "1.1.2", "dev": true}, "combined-stream": {"version": "1.0.8", "dev": true, "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.9.0", "dev": true, "requires": {"graceful-readlink": ">= 1.0.0"}}, "component-bind": {"version": "1.0.0", "dev": true}, "component-emitter": {"version": "1.2.1"}, "component-inherit": {"version": "0.0.3", "dev": true}, "compress-commons": {"version": "1.2.2", "dev": true, "requires": {"buffer-crc32": "^0.2.1", "crc32-stream": "^2.0.0", "normalize-path": "^2.0.0", "readable-stream": "^2.0.0"}}, "concat-map": {"version": "0.0.1"}, "concat-stream": {"version": "1.6.2", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "connect": {"version": "3.7.0", "dev": true, "requires": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}}, "connect-livereload": {"version": "0.6.1", "dev": true}, "console-browserify": {"version": "1.1.0", "dev": true, "requires": {"date-now": "^0.1.4"}}, "console-control-strings": {"version": "1.1.0", "dev": true, "optional": true}, "content-type": {"version": "1.0.4", "dev": true}, "continuable-cache": {"version": "0.3.1", "dev": true}, "convert-source-map": {"version": "1.1.3", "dev": true}, "cookie": {"version": "0.3.1", "dev": true}, "cookiejar": {"version": "2.1.2", "dev": true}, "copy-descriptor": {"version": "0.1.1"}, "copy-props": {"version": "2.0.4", "requires": {"each-props": "^1.3.0", "is-plain-object": "^2.0.1"}}, "core-util-is": {"version": "1.0.2"}, "crc": {"version": "3.8.0", "dev": true, "requires": {"buffer": "^5.1.0"}}, "crc32-stream": {"version": "2.0.0", "dev": true, "requires": {"crc": "^3.4.4", "readable-stream": "^2.0.0"}}, "cson": {"version": "5.1.0", "dev": true, "requires": {"coffee-script": "^1.12.7", "cson-parser": "^1.3.4", "editions": "^1.3.3", "extract-opts": "^3.3.1", "requirefresh": "^2.1.0", "safefs": "^4.1.0"}, "dependencies": {"coffee-script": {"version": "1.12.7", "dev": true}}}, "cson-parser": {"version": "1.3.5", "dev": true, "requires": {"coffee-script": "^1.10.0"}}, "CSSselect": {"version": "0.7.0", "dev": true, "requires": {"boolbase": "~1.0.0", "CSSwhat": "0.4", "domutils": "1.4", "nth-check": "~1.0.0"}, "dependencies": {"domutils": {"version": "1.4.3", "dev": true, "requires": {"domelementtype": "1"}}}}, "CSSwhat": {"version": "0.4.7", "dev": true}, "currently-unhandled": {"version": "0.4.1", "dev": true, "requires": {"array-find-index": "^1.0.1"}}, "custom-event": {"version": "1.0.1", "dev": true}, "d": {"version": "1.0.1", "requires": {"es5-ext": "^0.10.50", "type": "^1.0.1"}}, "dashdash": {"version": "1.14.1", "dev": true, "optional": true, "requires": {"assert-plus": "^1.0.0"}}, "data-uri-to-buffer": {"version": "2.0.1", "dev": true, "requires": {"@types/node": "^8.0.7"}}, "date-format": {"version": "2.1.0", "dev": true}, "date-now": {"version": "0.1.4", "dev": true}, "dateformat": {"version": "1.0.12", "dev": true, "requires": {"get-stdin": "^4.0.1", "meow": "^3.3.0"}}, "debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "decamelize": {"version": "1.2.0"}, "decode-uri-component": {"version": "0.2.0"}, "decompress-response": {"version": "4.2.1", "dev": true, "optional": true, "requires": {"mimic-response": "^2.0.0"}}, "deep-extend": {"version": "0.6.0", "dev": true, "optional": true}, "deep-is": {"version": "0.1.3", "dev": true}, "default-compare": {"version": "1.0.0", "requires": {"kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0"}}}, "default-resolution": {"version": "2.0.0"}, "define-properties": {"version": "1.1.2", "requires": {"foreach": "^2.0.5", "object-keys": "^1.0.8"}}, "define-property": {"version": "2.0.2", "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "isobject": {"version": "3.0.1"}, "kind-of": {"version": "6.0.2"}}}, "degenerator": {"version": "1.0.4", "dev": true, "requires": {"ast-types": "0.x.x", "escodegen": "1.x.x", "esprima": "3.x.x"}, "dependencies": {"esprima": {"version": "3.1.3", "dev": true}}}, "delayed-stream": {"version": "1.0.0", "dev": true}, "delegates": {"version": "1.0.0", "dev": true, "optional": true}, "depd": {"version": "1.1.2", "dev": true}, "destroy": {"version": "1.0.4", "dev": true}, "detect-file": {"version": "1.0.0"}, "detect-libc": {"version": "1.0.3", "dev": true, "optional": true}, "di": {"version": "0.0.1", "dev": true}, "dom-serialize": {"version": "2.2.1", "dev": true, "requires": {"custom-event": "~1.0.0", "ent": "~2.2.0", "extend": "^3.0.0", "void-elements": "^2.0.0"}}, "dom-serializer": {"version": "0.2.1", "dev": true, "requires": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "dependencies": {"domelementtype": {"version": "2.0.1", "dev": true}, "entities": {"version": "2.0.0", "dev": true}}}, "domelementtype": {"version": "1.3.0", "dev": true}, "domhandler": {"version": "2.3.0", "dev": true, "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.5.1", "dev": true, "requires": {"dom-serializer": "0", "domelementtype": "1"}}, "duplexer": {"version": "0.1.1", "dev": true}, "duplexify": {"version": "3.7.1", "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "each-props": {"version": "1.3.2", "requires": {"is-plain-object": "^2.0.1", "object.defaults": "^1.1.0"}}, "eachr": {"version": "3.3.0", "dev": true, "requires": {"editions": "^2.2.0", "typechecker": "^4.9.0"}, "dependencies": {"editions": {"version": "2.3.0", "dev": true, "requires": {"errlop": "^2.0.0", "semver": "^6.3.0"}}, "semver": {"version": "6.3.0", "dev": true}}}, "ecc-jsbn": {"version": "0.1.2", "dev": true, "optional": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "editions": {"version": "1.3.4", "dev": true}, "ee-first": {"version": "1.1.1", "dev": true}, "encodeurl": {"version": "1.0.2", "dev": true}, "end-of-stream": {"version": "1.4.1", "requires": {"once": "^1.4.0"}}, "engine.io": {"version": "3.2.1", "dev": true, "requires": {"accepts": "~1.3.4", "base64id": "1.0.0", "cookie": "0.3.1", "debug": "~3.1.0", "engine.io-parser": "~2.1.0", "ws": "~3.3.1"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}, "ws": {"version": "3.3.3", "dev": true, "requires": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}}}, "engine.io-client": {"version": "3.2.1", "dev": true, "requires": {"component-emitter": "1.2.1", "component-inherit": "0.0.3", "debug": "~3.1.0", "engine.io-parser": "~2.1.1", "has-cors": "1.1.0", "indexof": "0.0.1", "parseqs": "0.0.5", "parseuri": "0.0.5", "ws": "~3.3.1", "xmlhttprequest-ssl": "~1.5.4", "yeast": "0.1.2"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}, "ws": {"version": "3.3.3", "dev": true, "requires": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}}}, "engine.io-parser": {"version": "2.1.3", "dev": true, "requires": {"after": "0.8.2", "arraybuffer.slice": "~0.0.7", "base64-arraybuffer": "0.1.5", "blob": "0.0.5", "has-binary2": "~1.0.2"}}, "ent": {"version": "2.2.0", "dev": true}, "entities": {"version": "1.0.0", "dev": true}, "errlop": {"version": "2.0.0", "dev": true}, "errno": {"version": "0.1.7", "dev": true, "optional": true, "requires": {"prr": "~1.0.1"}}, "error": {"version": "7.0.2", "dev": true, "requires": {"string-template": "~0.2.1", "xtend": "~4.0.0"}}, "error-ex": {"version": "1.3.1", "requires": {"is-arrayish": "^0.2.1"}}, "es5-ext": {"version": "0.10.50", "requires": {"es6-iterator": "~2.0.3", "es6-symbol": "~3.1.1", "next-tick": "^1.0.0"}}, "es6-iterator": {"version": "2.0.3", "requires": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "es6-promise": {"version": "4.2.8", "dev": true}, "es6-promisify": {"version": "5.0.0", "dev": true, "requires": {"es6-promise": "^4.0.3"}}, "es6-set": {"version": "0.1.5", "dev": true, "requires": {"d": "1", "es5-ext": "~0.10.14", "es6-iterator": "~2.0.1", "es6-symbol": "3.1.1", "event-emitter": "~0.3.5"}}, "es6-symbol": {"version": "3.1.1", "requires": {"d": "1", "es5-ext": "~0.10.14"}}, "es6-weak-map": {"version": "2.0.3", "requires": {"d": "1", "es5-ext": "^0.10.46", "es6-iterator": "^2.0.3", "es6-symbol": "^3.1.1"}}, "escape-html": {"version": "1.0.3", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "dev": true}, "escodegen": {"version": "1.12.0", "dev": true, "requires": {"esprima": "^3.1.3", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "dependencies": {"esprima": {"version": "3.1.3", "dev": true}}}, "esprima": {"version": "2.7.3", "dev": true}, "estraverse": {"version": "4.3.0", "dev": true}, "esutils": {"version": "2.0.3", "dev": true}, "etag": {"version": "1.8.1", "dev": true}, "event-emitter": {"version": "0.3.5", "dev": true, "requires": {"d": "1", "es5-ext": "~0.10.14"}}, "eventemitter2": {"version": "0.4.14", "dev": true}, "eventemitter3": {"version": "4.0.0", "dev": true}, "events": {"version": "1.1.1", "dev": true}, "exit": {"version": "0.1.2", "dev": true}, "expand-template": {"version": "2.0.3", "dev": true, "optional": true}, "expand-tilde": {"version": "2.0.2", "requires": {"homedir-polyfill": "^1.0.1"}}, "extend": {"version": "3.0.1"}, "extend-shallow": {"version": "3.0.2", "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "requires": {"is-plain-object": "^2.0.4"}}}}, "extract-opts": {"version": "3.4.0", "dev": true, "requires": {"eachr": "^3.2.0", "editions": "^2.2.0", "typechecker": "^4.9.0"}, "dependencies": {"editions": {"version": "2.3.0", "dev": true, "requires": {"errlop": "^2.0.0", "semver": "^6.3.0"}}, "semver": {"version": "6.3.0", "dev": true}}}, "extract-zip": {"version": "1.6.7", "dev": true, "requires": {"concat-stream": "1.6.2", "debug": "2.6.9", "mkdirp": "0.5.1", "yauzl": "2.4.1"}}, "extsprintf": {"version": "1.3.0", "dev": true, "optional": true}, "fancy-log": {"version": "1.3.3", "requires": {"ansi-gray": "^0.1.1", "color-support": "^1.1.3", "parse-node-version": "^1.0.0", "time-stamp": "^1.0.0"}}, "fast-deep-equal": {"version": "2.0.1", "dev": true, "optional": true}, "fast-json-stable-stringify": {"version": "2.0.0", "dev": true, "optional": true}, "fast-levenshtein": {"version": "2.0.6", "dev": true}, "faye-websocket": {"version": "0.10.0", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}, "fd-slicer": {"version": "1.0.1", "dev": true, "requires": {"pend": "~1.2.0"}}, "figures": {"version": "1.7.0", "dev": true, "requires": {"escape-string-regexp": "^1.0.5", "object-assign": "^4.1.0"}}, "file-sync-cmp": {"version": "0.1.1", "dev": true}, "file-uri-to-path": {"version": "1.0.0", "dev": true}, "filendir": {"version": "1.0.2", "dev": true, "requires": {"mkdirp": "^0.5.0"}}, "finalhandler": {"version": "1.1.2", "dev": true, "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}}, "find-up": {"version": "1.1.2", "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "findup-sync": {"version": "0.3.0", "dev": true, "requires": {"glob": "~5.0.0"}, "dependencies": {"glob": {"version": "5.0.15", "dev": true, "requires": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "fined": {"version": "1.2.0", "requires": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}}, "flagged-respawn": {"version": "1.0.1"}, "flatted": {"version": "2.0.1", "dev": true}, "flush-write-stream": {"version": "1.1.1", "requires": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "follow-redirects": {"version": "1.10.0", "dev": true, "requires": {"debug": "^3.0.0"}, "dependencies": {"debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "for-in": {"version": "1.0.2"}, "foreach": {"version": "2.0.5"}, "forever-agent": {"version": "0.6.1", "dev": true, "optional": true}, "form-data": {"version": "2.3.3", "dev": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "formidable": {"version": "1.2.1", "dev": true}, "fragment-cache": {"version": "0.2.1", "requires": {"map-cache": "^0.2.2"}}, "fresh": {"version": "0.5.2", "dev": true}, "fs-constants": {"version": "1.0.0", "dev": true}, "fs-extra": {"version": "6.0.1", "dev": true, "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs-mkdirp-stream": {"version": "1.0.0", "requires": {"graceful-fs": "^4.1.11", "through2": "^2.0.3"}}, "fs.realpath": {"version": "1.0.0"}, "fsevents": {"version": "2.1.2", "dev": true, "optional": true}, "ftp": {"version": "0.3.10", "dev": true, "requires": {"readable-stream": "1.1.x", "xregexp": "2.0.0"}, "dependencies": {"isarray": {"version": "0.0.1", "dev": true}, "readable-stream": {"version": "1.1.14", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "dev": true}}}, "function-bind": {"version": "1.1.1"}, "gauge": {"version": "2.7.4", "dev": true, "optional": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "gaze": {"version": "1.1.3", "dev": true, "requires": {"globule": "^1.0.0"}}, "get-caller-file": {"version": "1.0.3"}, "get-stdin": {"version": "4.0.1", "dev": true}, "get-uri": {"version": "2.0.3", "dev": true, "requires": {"data-uri-to-buffer": "2", "debug": "4", "extend": "~3.0.2", "file-uri-to-path": "1", "ftp": "~0.3.10", "readable-stream": "3"}, "dependencies": {"debug": {"version": "4.1.1", "dev": true, "requires": {"ms": "^2.1.1"}}, "extend": {"version": "3.0.2", "dev": true}, "ms": {"version": "2.1.2", "dev": true}, "readable-stream": {"version": "3.4.0", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}}}, "get-value": {"version": "2.0.6"}, "getobject": {"version": "0.1.0", "dev": true}, "getpass": {"version": "0.1.7", "dev": true, "optional": true, "requires": {"assert-plus": "^1.0.0"}}, "github-from-package": {"version": "0.0.0", "dev": true, "optional": true}, "glob": {"version": "7.0.6", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.2", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.0", "dev": true, "requires": {"is-glob": "^4.0.1"}, "dependencies": {"is-glob": {"version": "4.0.1", "dev": true, "requires": {"is-extglob": "^2.1.1"}}}}, "glob-stream": {"version": "6.1.0", "requires": {"extend": "^3.0.0", "glob": "^7.1.1", "glob-parent": "^3.1.0", "is-negated-glob": "^1.0.0", "ordered-read-streams": "^1.0.0", "pumpify": "^1.3.5", "readable-stream": "^2.1.5", "remove-trailing-separator": "^1.0.1", "to-absolute-glob": "^2.0.0", "unique-stream": "^2.0.2"}, "dependencies": {"glob": {"version": "7.1.4", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "is-extglob": {"version": "2.1.1"}, "is-glob": {"version": "3.1.0", "requires": {"is-extglob": "^2.1.0"}}}}, "glob-watcher": {"version": "5.0.3", "requires": {"anymatch": "^2.0.0", "async-done": "^1.2.0", "chokidar": "^2.0.0", "is-negated-glob": "^1.0.0", "just-debounce": "^1.0.0", "object.defaults": "^1.1.0"}, "dependencies": {"anymatch": {"version": "2.0.0", "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "arr-diff": {"version": "4.0.0"}, "array-unique": {"version": "0.3.2"}, "braces": {"version": "2.3.2", "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "chokidar": {"version": "2.1.8", "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "dependencies": {"normalize-path": {"version": "3.0.0"}}}, "expand-brackets": {"version": "2.1.4", "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "0.1.6", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "is-data-descriptor": {"version": "0.1.4", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "is-descriptor": {"version": "0.1.6", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}}, "kind-of": {"version": "5.1.0"}}}, "extglob": {"version": "2.0.4", "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "fill-range": {"version": "4.0.0", "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "fsevents": {"version": "1.2.9", "optional": true, "requires": {"nan": "^2.12.1", "node-pre-gyp": "^0.12.0"}, "dependencies": {"abbrev": {"version": "1.1.1", "bundled": true, "optional": true}, "ansi-regex": {"version": "2.1.1", "bundled": true, "optional": true}, "aproba": {"version": "1.2.0", "bundled": true, "optional": true}, "are-we-there-yet": {"version": "1.1.5", "bundled": true, "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "balanced-match": {"version": "1.0.0", "bundled": true, "optional": true}, "brace-expansion": {"version": "1.1.11", "bundled": true, "optional": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "chownr": {"version": "1.1.1", "bundled": true, "optional": true}, "code-point-at": {"version": "1.1.0", "bundled": true, "optional": true}, "concat-map": {"version": "0.0.1", "bundled": true, "optional": true}, "console-control-strings": {"version": "1.1.0", "bundled": true, "optional": true}, "core-util-is": {"version": "1.0.2", "bundled": true, "optional": true}, "debug": {"version": "4.1.1", "bundled": true, "optional": true, "requires": {"ms": "^2.1.1"}}, "deep-extend": {"version": "0.6.0", "bundled": true, "optional": true}, "delegates": {"version": "1.0.0", "bundled": true, "optional": true}, "detect-libc": {"version": "1.0.3", "bundled": true, "optional": true}, "fs-minipass": {"version": "1.2.5", "bundled": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "optional": true}, "gauge": {"version": "2.7.4", "bundled": true, "optional": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "glob": {"version": "7.1.3", "bundled": true, "optional": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "has-unicode": {"version": "2.0.1", "bundled": true, "optional": true}, "iconv-lite": {"version": "0.4.24", "bundled": true, "optional": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore-walk": {"version": "3.0.1", "bundled": true, "optional": true, "requires": {"minimatch": "^3.0.4"}}, "inflight": {"version": "1.0.6", "bundled": true, "optional": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true, "optional": true}, "ini": {"version": "1.3.5", "bundled": true, "optional": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "optional": true, "requires": {"number-is-nan": "^1.0.0"}}, "isarray": {"version": "1.0.0", "bundled": true, "optional": true}, "minimatch": {"version": "3.0.4", "bundled": true, "optional": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "optional": true}, "minipass": {"version": "2.3.5", "bundled": true, "optional": true, "requires": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "minizlib": {"version": "1.2.1", "bundled": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "mkdirp": {"version": "0.5.1", "bundled": true, "optional": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.1.1", "bundled": true, "optional": true}, "needle": {"version": "2.3.0", "bundled": true, "optional": true, "requires": {"debug": "^4.1.0", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}}, "node-pre-gyp": {"version": "0.12.0", "bundled": true, "optional": true, "requires": {"detect-libc": "^1.0.2", "mkdirp": "^0.5.1", "needle": "^2.2.1", "nopt": "^4.0.1", "npm-packlist": "^1.1.6", "npmlog": "^4.0.2", "rc": "^1.2.7", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^4"}}, "nopt": {"version": "4.0.1", "bundled": true, "optional": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "npm-bundled": {"version": "1.0.6", "bundled": true, "optional": true}, "npm-packlist": {"version": "1.4.1", "bundled": true, "optional": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "npmlog": {"version": "4.1.2", "bundled": true, "optional": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "optional": true}, "object-assign": {"version": "4.1.1", "bundled": true, "optional": true}, "once": {"version": "1.4.0", "bundled": true, "optional": true, "requires": {"wrappy": "1"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "optional": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true, "optional": true}, "osenv": {"version": "0.1.5", "bundled": true, "optional": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "optional": true}, "process-nextick-args": {"version": "2.0.0", "bundled": true, "optional": true}, "rc": {"version": "1.2.8", "bundled": true, "optional": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "bundled": true, "optional": true}}}, "readable-stream": {"version": "2.3.6", "bundled": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "rimraf": {"version": "2.6.3", "bundled": true, "optional": true, "requires": {"glob": "^7.1.3"}}, "safe-buffer": {"version": "5.1.2", "bundled": true, "optional": true}, "safer-buffer": {"version": "2.1.2", "bundled": true, "optional": true}, "sax": {"version": "1.2.4", "bundled": true, "optional": true}, "semver": {"version": "5.7.0", "bundled": true, "optional": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "optional": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "optional": true}, "string_decoder": {"version": "1.1.1", "bundled": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}, "string-width": {"version": "1.0.2", "bundled": true, "optional": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "optional": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-json-comments": {"version": "2.0.1", "bundled": true, "optional": true}, "tar": {"version": "4.4.8", "bundled": true, "optional": true, "requires": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}}, "util-deprecate": {"version": "1.0.2", "bundled": true, "optional": true}, "wide-align": {"version": "1.1.3", "bundled": true, "optional": true, "requires": {"string-width": "^1.0.2 || 2"}}, "wrappy": {"version": "1.0.2", "bundled": true, "optional": true}, "yallist": {"version": "3.0.3", "bundled": true, "optional": true}}}, "glob-parent": {"version": "3.1.0", "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "requires": {"is-extglob": "^2.1.0"}}}}, "is-accessor-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "is-extglob": {"version": "2.1.1"}, "is-glob": {"version": "4.0.1", "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "3.0.0", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "isobject": {"version": "3.0.1"}, "kind-of": {"version": "6.0.2"}, "micromatch": {"version": "3.1.10", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "nan": {"version": "2.14.0", "optional": true}, "readdirp": {"version": "2.2.1", "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}}}, "global-modules": {"version": "1.0.0", "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}, "global-prefix": {"version": "1.0.2", "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}}, "globule": {"version": "1.2.1", "dev": true, "requires": {"glob": "~7.1.1", "lodash": "~4.17.10", "minimatch": "~3.0.2"}, "dependencies": {"glob": {"version": "7.1.4", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "glogg": {"version": "1.0.2", "requires": {"sparkles": "^1.0.0"}}, "graceful-fs": {"version": "4.1.11"}, "graceful-readlink": {"version": "1.0.1", "dev": true}, "grunt": {"version": "1.0.4", "dev": true, "requires": {"coffeescript": "~1.10.0", "dateformat": "~1.0.12", "eventemitter2": "~0.4.13", "exit": "~0.1.1", "findup-sync": "~0.3.0", "glob": "~7.0.0", "grunt-cli": "~1.2.0", "grunt-known-options": "~1.1.0", "grunt-legacy-log": "~2.0.0", "grunt-legacy-util": "~1.1.1", "iconv-lite": "~0.4.13", "js-yaml": "~3.13.0", "minimatch": "~3.0.2", "mkdirp": "~0.5.1", "nopt": "~3.0.6", "path-is-absolute": "~1.0.0", "rimraf": "~2.6.2"}, "dependencies": {"coffeescript": {"version": "1.10.0", "dev": true}, "esprima": {"version": "4.0.1", "dev": true}, "grunt-cli": {"version": "1.2.0", "dev": true, "requires": {"findup-sync": "~0.3.0", "grunt-known-options": "~1.1.0", "nopt": "~3.0.6", "resolve": "~1.1.0"}}, "js-yaml": {"version": "3.13.1", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "rimraf": {"version": "2.6.3", "dev": true, "requires": {"glob": "^7.1.3"}, "dependencies": {"glob": {"version": "7.1.4", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}}}, "grunt-angular-templates": {"version": "1.2.0", "dev": true, "requires": {"html-minifier": "~4.0.0"}}, "grunt-browser-output": {"version": "1.0.3", "dev": true, "requires": {"ansi-to-html": "~0.1.1", "hooker": "~0.2.3", "ws": "~0.4.31"}}, "grunt-cache-bust": {"version": "1.7.0", "dev": true, "requires": {"fs-extra": "^6.0.1"}}, "grunt-contrib-clean": {"version": "2.0.0", "dev": true, "requires": {"async": "^2.6.1", "rimraf": "^2.6.2"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}}, "glob": {"version": "7.1.4", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "lodash": {"version": "4.17.15", "dev": true}, "rimraf": {"version": "2.7.1", "dev": true, "requires": {"glob": "^7.1.3"}}}}, "grunt-contrib-compress": {"version": "1.6.0", "dev": true, "requires": {"archiver": "^1.3.0", "chalk": "^1.1.1", "iltorb": "^2.4.3", "lodash": "^4.7.0", "pretty-bytes": "^4.0.2", "stream-buffers": "^2.1.0"}}, "grunt-contrib-concat": {"version": "1.0.1", "dev": true, "requires": {"chalk": "^1.0.0", "source-map": "^0.5.3"}, "dependencies": {"source-map": {"version": "0.5.7", "dev": true}}}, "grunt-contrib-connect": {"version": "2.1.0", "dev": true, "requires": {"async": "^2.6.1", "connect": "^3.6.6", "connect-livereload": "^0.6.0", "morgan": "^1.9.1", "node-http2": "^4.0.1", "opn": "^5.3.0", "portscanner": "^2.2.0", "serve-index": "^1.9.1", "serve-static": "^1.13.2"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}}, "lodash": {"version": "4.17.15", "dev": true}}}, "grunt-contrib-copy": {"version": "1.0.0", "dev": true, "requires": {"chalk": "^1.1.1", "file-sync-cmp": "^0.1.0"}}, "grunt-contrib-cssmin": {"version": "3.0.0", "dev": true, "requires": {"chalk": "^2.4.1", "clean-css": "~4.2.1", "maxmin": "^2.1.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "grunt-contrib-htmlmin": {"version": "3.1.0", "dev": true, "requires": {"chalk": "^2.4.2", "html-minifier": "^4.0.0", "pretty-bytes": "^5.1.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "pretty-bytes": {"version": "5.3.0", "dev": true}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "grunt-contrib-jshint": {"version": "2.1.0", "dev": true, "requires": {"chalk": "^2.4.2", "hooker": "^0.2.3", "jshint": "~2.10.2"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "grunt-contrib-less": {"version": "2.0.0", "dev": true, "requires": {"async": "^2.0.0", "chalk": "^1.0.0", "less": "^3.0.4", "lodash": "^4.17.10"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}, "dependencies": {"lodash": {"version": "4.17.15", "dev": true}}}}}, "grunt-contrib-uglify": {"version": "4.0.1", "dev": true, "requires": {"chalk": "^2.4.1", "maxmin": "^2.1.0", "uglify-js": "^3.5.0", "uri-path": "^1.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "grunt-contrib-watch": {"version": "1.1.0", "dev": true, "requires": {"async": "^2.6.0", "gaze": "^1.1.0", "lodash": "^4.17.10", "tiny-lr": "^1.1.1"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}, "dependencies": {"lodash": {"version": "4.17.15", "dev": true}}}}}, "grunt-dom-munger": {"version": "3.4.0", "dev": true, "requires": {"cheerio": "~0.12.3"}}, "grunt-gh-pages": {"version": "3.1.0", "dev": true, "requires": {"async": "2.0.1", "fs-extra": "^0.30.0", "graceful-fs": "4.1.5", "q": "0.9.3", "q-io": "^1.13.6", "url-safe": "^2.0.0"}, "dependencies": {"async": {"version": "2.0.1", "dev": true, "requires": {"lodash": "^4.8.0"}}, "fs-extra": {"version": "0.30.0", "dev": true, "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0", "path-is-absolute": "^1.0.0", "rimraf": "^2.2.8"}}, "glob": {"version": "7.1.4", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "graceful-fs": {"version": "4.1.5", "dev": true}, "jsonfile": {"version": "2.4.0", "dev": true, "requires": {"graceful-fs": "^4.1.6"}, "dependencies": {"graceful-fs": {"version": "4.2.2", "dev": true, "optional": true}}}, "rimraf": {"version": "2.7.1", "dev": true, "requires": {"glob": "^7.1.3"}}}}, "grunt-html-angular-validate": {"version": "0.6.1", "dev": true, "requires": {"colors": "~1.1.2", "html-angular-validate": "~0.2.3"}}, "grunt-karma": {"version": "3.0.2", "dev": true, "requires": {"lodash": "^4.17.10"}}, "grunt-known-options": {"version": "1.1.1", "dev": true}, "grunt-legacy-log": {"version": "2.0.0", "dev": true, "requires": {"colors": "~1.1.2", "grunt-legacy-log-utils": "~2.0.0", "hooker": "~0.2.3", "lodash": "~4.17.5"}}, "grunt-legacy-log-utils": {"version": "2.0.1", "dev": true, "requires": {"chalk": "~2.4.1", "lodash": "~4.17.10"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "grunt-legacy-util": {"version": "1.1.1", "dev": true, "requires": {"async": "~1.5.2", "exit": "~0.1.1", "getobject": "~0.1.0", "hooker": "~0.2.3", "lodash": "~4.17.10", "underscore.string": "~3.3.4", "which": "~1.3.0"}, "dependencies": {"which": {"version": "1.3.1", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "grunt-ng-annotate": {"version": "3.0.0", "dev": true, "requires": {"lodash.clonedeep": "^4.5.0", "ng-annotate": "^1.2.1"}}, "grunt-replace": {"version": "1.0.1", "dev": true, "requires": {"applause": "1.2.2", "chalk": "^1.1.0", "file-sync-cmp": "^0.1.0", "lodash": "^4.11.0"}}, "grunt-traceur": {"version": "0.5.5", "dev": true, "requires": {"es6-promise": "^1.0.0", "lodash": "^2.4.1"}, "dependencies": {"es6-promise": {"version": "1.0.0", "dev": true}, "lodash": {"version": "2.4.2", "dev": true}}}, "gulp": {"version": "4.0.2", "requires": {"glob-watcher": "^5.0.3", "gulp-cli": "^2.2.0", "undertaker": "^1.2.1", "vinyl-fs": "^3.0.0"}, "dependencies": {"camelcase": {"version": "3.0.0"}, "cliui": {"version": "3.2.0", "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "gulp-cli": {"version": "2.2.0", "requires": {"ansi-colors": "^1.0.1", "archy": "^1.0.0", "array-sort": "^1.0.0", "color-support": "^1.1.3", "concat-stream": "^1.6.0", "copy-props": "^2.0.1", "fancy-log": "^1.3.2", "gulplog": "^1.0.0", "interpret": "^1.1.0", "isobject": "^3.0.1", "liftoff": "^3.1.0", "matchdep": "^2.0.0", "mute-stdout": "^1.0.0", "pretty-hrtime": "^1.0.0", "replace-homedir": "^1.0.0", "semver-greatest-satisfied-range": "^1.1.0", "v8flags": "^3.0.1", "yargs": "^7.1.0"}}, "isobject": {"version": "3.0.1"}, "yargs": {"version": "7.1.0", "requires": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.0"}}}}, "gulplog": {"version": "1.0.0", "requires": {"glogg": "^1.0.0"}}, "gzip-size": {"version": "3.0.0", "dev": true, "requires": {"duplexer": "^0.1.1"}}, "har-schema": {"version": "2.0.0", "dev": true, "optional": true}, "har-validator": {"version": "5.1.3", "dev": true, "optional": true, "requires": {"ajv": "^6.5.5", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-ansi": {"version": "2.0.0", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "has-binary2": {"version": "1.0.3", "dev": true, "requires": {"isarray": "2.0.1"}, "dependencies": {"isarray": {"version": "2.0.1", "dev": true}}}, "has-cors": {"version": "1.1.0", "dev": true}, "has-flag": {"version": "3.0.0", "dev": true}, "has-symbols": {"version": "1.0.0"}, "has-unicode": {"version": "2.0.1", "dev": true, "optional": true}, "has-value": {"version": "1.0.0", "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "dependencies": {"isobject": {"version": "3.0.1"}}}, "has-values": {"version": "1.0.0", "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"is-number": {"version": "3.0.0", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "kind-of": {"version": "4.0.0", "requires": {"is-buffer": "^1.1.5"}}}}, "he": {"version": "1.2.0", "dev": true}, "homedir-polyfill": {"version": "1.0.3", "requires": {"parse-passwd": "^1.0.0"}}, "hooker": {"version": "0.2.3", "dev": true}, "hosted-git-info": {"version": "2.6.0"}, "html-angular-validate": {"version": "0.2.3", "dev": true, "requires": {"async": "^2.6.0", "filendir": "~1.0.0", "globule": "^1.2.0", "node.extend": "^2.0.0", "string.prototype.endswith": "~0.2.0", "w3cjs": "^0.4.0", "xmlbuilder": "^9.0.4"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}}, "lodash": {"version": "4.17.15", "dev": true}}}, "html-minifier": {"version": "4.0.0", "dev": true, "requires": {"camel-case": "^3.0.0", "clean-css": "^4.2.1", "commander": "^2.19.0", "he": "^1.2.0", "param-case": "^2.1.1", "relateurl": "^0.2.7", "uglify-js": "^3.5.1"}, "dependencies": {"commander": {"version": "2.20.0", "dev": true}}}, "htmlparser2": {"version": "3.8.3", "dev": true, "requires": {"domelementtype": "1", "domhandler": "2.3", "domutils": "1.5", "entities": "1.0", "readable-stream": "1.1"}, "dependencies": {"isarray": {"version": "0.0.1", "dev": true}, "readable-stream": {"version": "1.1.14", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "dev": true}}}, "http-errors": {"version": "1.6.3", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "http-parser-js": {"version": "0.4.10", "dev": true}, "http-proxy": {"version": "1.18.0", "dev": true, "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-agent": {"version": "2.1.0", "dev": true, "requires": {"agent-base": "4", "debug": "3.1.0"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}}}, "http-signature": {"version": "1.2.0", "dev": true, "optional": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-browserify": {"version": "0.0.1", "dev": true}, "https-proxy-agent": {"version": "2.2.4", "dev": true, "requires": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "dependencies": {"debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "iconv-lite": {"version": "0.4.24", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.1.13", "dev": true}, "iltorb": {"version": "2.4.5", "dev": true, "optional": true, "requires": {"detect-libc": "^1.0.3", "nan": "^2.14.0", "npmlog": "^4.1.2", "prebuild-install": "^5.3.3", "which-pm-runs": "^1.0.0"}, "dependencies": {"nan": {"version": "2.14.0", "dev": true, "optional": true}}}, "image-size": {"version": "0.5.5", "dev": true, "optional": true}, "indent-string": {"version": "2.1.0", "dev": true, "requires": {"repeating": "^2.0.0"}}, "indexof": {"version": "0.0.1", "dev": true}, "inflight": {"version": "1.0.6", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3"}, "ini": {"version": "1.3.5"}, "interpret": {"version": "1.2.0"}, "invert-kv": {"version": "1.0.0"}, "ip": {"version": "1.1.5", "dev": true}, "irregular-plurals": {"version": "1.4.0", "dev": true}, "is": {"version": "3.3.0", "dev": true}, "is-absolute": {"version": "1.0.0", "requires": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}}, "is-accessor-descriptor": {"version": "0.1.6", "requires": {"kind-of": "^3.0.2"}}, "is-arrayish": {"version": "0.2.1"}, "is-binary-path": {"version": "1.0.1", "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "1.1.6"}, "is-builtin-module": {"version": "1.0.0", "requires": {"builtin-modules": "^1.0.0"}}, "is-data-descriptor": {"version": "0.1.4", "requires": {"kind-of": "^3.0.2"}}, "is-descriptor": {"version": "0.1.6", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0"}}}, "is-extendable": {"version": "0.1.1"}, "is-extglob": {"version": "2.1.1", "dev": true}, "is-finite": {"version": "1.0.2", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "is-fullwidth-code-point": {"version": "1.0.0", "requires": {"number-is-nan": "^1.0.0"}}, "is-negated-glob": {"version": "1.0.0"}, "is-number-like": {"version": "1.0.8", "dev": true, "requires": {"lodash.isfinite": "^3.3.2"}}, "is-plain-object": {"version": "2.0.4", "requires": {"isobject": "^3.0.1"}, "dependencies": {"isobject": {"version": "3.0.1"}}}, "is-relative": {"version": "1.0.0", "requires": {"is-unc-path": "^1.0.0"}}, "is-typedarray": {"version": "1.0.0", "dev": true, "optional": true}, "is-unc-path": {"version": "1.0.0", "requires": {"unc-path-regex": "^0.1.2"}}, "is-utf8": {"version": "0.2.1"}, "is-valid-glob": {"version": "1.0.0"}, "is-windows": {"version": "1.0.2"}, "is-wsl": {"version": "1.1.0", "dev": true}, "isarray": {"version": "1.0.0"}, "isbinaryfile": {"version": "3.0.3", "dev": true, "requires": {"buffer-alloc": "^1.2.0"}}, "isexe": {"version": "2.0.0"}, "isstream": {"version": "0.1.2", "dev": true}, "jasmine-core": {"version": "3.5.0", "dev": true}, "jit-grunt": {"version": "0.10.0", "dev": true, "requires": {}}, "js-yaml": {"version": "3.5.5", "dev": true, "requires": {"argparse": "^1.0.2", "esprima": "^2.6.0"}}, "jsbn": {"version": "0.1.1", "dev": true, "optional": true}, "jshint": {"version": "2.10.2", "dev": true, "requires": {"cli": "~1.0.0", "console-browserify": "1.1.x", "exit": "0.1.x", "htmlparser2": "3.8.x", "lodash": "~4.17.11", "minimatch": "~3.0.2", "shelljs": "0.3.x", "strip-json-comments": "1.0.x"}, "dependencies": {"lodash": {"version": "4.17.15", "dev": true}, "strip-json-comments": {"version": "1.0.4", "dev": true}}}, "jshint-stylish": {"version": "2.2.1", "dev": true, "requires": {"beeper": "^1.1.0", "chalk": "^1.0.0", "log-symbols": "^1.0.0", "plur": "^2.1.0", "string-length": "^1.0.0", "text-table": "^0.2.0"}}, "json-schema": {"version": "0.2.3", "dev": true, "optional": true}, "json-schema-traverse": {"version": "0.4.1", "dev": true, "optional": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1"}, "json-stringify-safe": {"version": "5.0.1", "dev": true, "optional": true}, "jsonfile": {"version": "4.0.0", "dev": true, "requires": {"graceful-fs": "^4.1.6"}}, "jsprim": {"version": "1.4.1", "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "just-debounce": {"version": "1.0.0"}, "karma": {"version": "4.4.1", "dev": true, "requires": {"bluebird": "^3.3.0", "body-parser": "^1.16.1", "braces": "^3.0.2", "chokidar": "^3.0.0", "colors": "^1.1.0", "connect": "^3.6.0", "di": "^0.0.1", "dom-serialize": "^2.2.0", "flatted": "^2.0.0", "glob": "^7.1.1", "graceful-fs": "^4.1.2", "http-proxy": "^1.13.0", "isbinaryfile": "^3.0.0", "lodash": "^4.17.14", "log4js": "^4.0.0", "mime": "^2.3.1", "minimatch": "^3.0.2", "optimist": "^0.6.1", "qjobs": "^1.1.4", "range-parser": "^1.2.0", "rimraf": "^2.6.0", "safe-buffer": "^5.0.1", "socket.io": "2.1.1", "source-map": "^0.6.1", "tmp": "0.0.33", "useragent": "2.3.0"}, "dependencies": {"braces": {"version": "3.0.2", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "fill-range": {"version": "7.0.1", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "glob": {"version": "7.1.6", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "is-number": {"version": "7.0.0", "dev": true}, "lodash": {"version": "4.17.15", "dev": true}, "mime": {"version": "2.4.4", "dev": true}, "rimraf": {"version": "2.7.1", "dev": true, "requires": {"glob": "^7.1.3"}}, "to-regex-range": {"version": "5.0.1", "dev": true, "requires": {"is-number": "^7.0.0"}}}}, "karma-chrome-launcher": {"version": "3.1.0", "dev": true, "requires": {"which": "^1.2.1"}}, "karma-jasmine": {"version": "3.1.1", "dev": true, "requires": {"jasmine-core": "^3.5.0"}}, "karma-junit-reporter": {"version": "2.0.1", "dev": true, "requires": {"path-is-absolute": "^1.0.0", "xmlbuilder": "12.0.0"}, "dependencies": {"xmlbuilder": {"version": "12.0.0", "dev": true}}}, "karma-mocha-reporter": {"version": "2.2.5", "dev": true, "requires": {"chalk": "^2.1.0", "log-symbols": "^2.1.0", "strip-ansi": "^4.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "dev": true}, "ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.1", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "log-symbols": {"version": "2.2.0", "dev": true, "requires": {"chalk": "^2.0.1"}}, "strip-ansi": {"version": "4.0.0", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "supports-color": {"version": "5.4.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "karma-ng-html2js-preprocessor": {"version": "1.0.0", "dev": true, "requires": {}}, "kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}, "klaw": {"version": "1.3.1", "dev": true, "requires": {"graceful-fs": "^4.1.9"}}, "last-run": {"version": "1.1.1", "requires": {"default-resolution": "^2.0.0", "es6-weak-map": "^2.0.1"}}, "lazystream": {"version": "1.0.0", "requires": {"readable-stream": "^2.0.5"}}, "lcid": {"version": "1.0.0", "requires": {"invert-kv": "^1.0.0"}}, "lead": {"version": "1.0.0", "requires": {"flush-write-stream": "^1.0.2"}}, "less": {"version": "3.10.3", "dev": true, "requires": {"clone": "^2.1.2", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}}, "levn": {"version": "0.3.0", "dev": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "liftoff": {"version": "3.1.0", "requires": {"extend": "^3.0.0", "findup-sync": "^3.0.0", "fined": "^1.0.1", "flagged-respawn": "^1.0.0", "is-plain-object": "^2.0.4", "object.map": "^1.0.0", "rechoir": "^0.6.2", "resolve": "^1.1.7"}, "dependencies": {"arr-diff": {"version": "4.0.0"}, "array-unique": {"version": "0.3.2"}, "braces": {"version": "2.3.2", "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "expand-brackets": {"version": "2.1.4", "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "0.1.6", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "is-data-descriptor": {"version": "0.1.4", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "is-descriptor": {"version": "0.1.6", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}}, "kind-of": {"version": "5.1.0"}}}, "extglob": {"version": "2.0.4", "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "fill-range": {"version": "4.0.0", "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "findup-sync": {"version": "3.0.0", "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "is-accessor-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "is-extglob": {"version": "2.1.1"}, "is-glob": {"version": "4.0.1", "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "3.0.0", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "isobject": {"version": "3.0.1"}, "kind-of": {"version": "6.0.2"}, "micromatch": {"version": "3.1.10", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}}}, "livereload-js": {"version": "2.4.0", "dev": true}, "load-grunt-config": {"version": "3.0.1", "dev": true, "requires": {"cson": "5.1.0", "glob": "7.1.4", "jit-grunt": "0.10.0", "js-yaml": "3.13.1", "load-grunt-tasks": "5.1.0", "lodash": "4.17.15"}, "dependencies": {"esprima": {"version": "4.0.1", "dev": true}, "glob": {"version": "7.1.4", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "js-yaml": {"version": "3.13.1", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "lodash": {"version": "4.17.15", "dev": true}}}, "load-grunt-tasks": {"version": "5.1.0", "dev": true, "requires": {"arrify": "^2.0.1", "multimatch": "^4.0.0", "pkg-up": "^3.1.0", "resolve-pkg": "^2.0.0"}}, "load-json-file": {"version": "1.1.0", "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}}, "locate-path": {"version": "3.0.0", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "dependencies": {"path-exists": {"version": "3.0.0", "dev": true}}}, "lodash": {"version": "4.17.10", "dev": true}, "lodash.clonedeep": {"version": "4.5.0", "dev": true}, "lodash.isfinite": {"version": "3.3.2", "dev": true}, "log-symbols": {"version": "1.0.2", "dev": true, "requires": {"chalk": "^1.0.0"}}, "log4js": {"version": "4.5.1", "dev": true, "requires": {"date-format": "^2.0.0", "debug": "^4.1.1", "flatted": "^2.0.0", "rfdc": "^1.1.4", "streamroller": "^1.0.6"}, "dependencies": {"debug": {"version": "4.1.1", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "loud-rejection": {"version": "1.6.0", "dev": true, "requires": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}}, "lower-case": {"version": "1.1.4", "dev": true}, "lru-cache": {"version": "4.1.5", "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "make-iterator": {"version": "1.0.1", "requires": {"kind-of": "^6.0.2"}, "dependencies": {"kind-of": {"version": "6.0.2"}}}, "map-cache": {"version": "0.2.2"}, "map-obj": {"version": "1.0.1", "dev": true}, "map-visit": {"version": "1.0.0", "requires": {"object-visit": "^1.0.0"}}, "matchdep": {"version": "2.0.0", "requires": {"findup-sync": "^2.0.0", "micromatch": "^3.0.4", "resolve": "^1.4.0", "stack-trace": "0.0.10"}, "dependencies": {"arr-diff": {"version": "4.0.0"}, "array-unique": {"version": "0.3.2"}, "braces": {"version": "2.3.2", "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "expand-brackets": {"version": "2.1.4", "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "0.1.6", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "is-data-descriptor": {"version": "0.1.4", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "is-descriptor": {"version": "0.1.6", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}}, "kind-of": {"version": "5.1.0"}}}, "extglob": {"version": "2.0.4", "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "fill-range": {"version": "4.0.0", "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "findup-sync": {"version": "2.0.0", "requires": {"detect-file": "^1.0.0", "is-glob": "^3.1.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "is-accessor-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "is-extglob": {"version": "2.1.1"}, "is-glob": {"version": "3.1.0", "requires": {"is-extglob": "^2.1.0"}}, "is-number": {"version": "3.0.0", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "requires": {"is-buffer": "^1.1.5"}}}}, "isobject": {"version": "3.0.1"}, "kind-of": {"version": "6.0.2"}, "micromatch": {"version": "3.1.10", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "resolve": {"version": "1.12.0", "requires": {"path-parse": "^1.0.6"}}}}, "maxmin": {"version": "2.1.0", "dev": true, "requires": {"chalk": "^1.0.0", "figures": "^1.0.1", "gzip-size": "^3.0.0", "pretty-bytes": "^3.0.0"}, "dependencies": {"pretty-bytes": {"version": "3.0.1", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}}}, "media-typer": {"version": "0.3.0", "dev": true}, "meow": {"version": "3.7.0", "dev": true, "requires": {"camelcase-keys": "^2.0.0", "decamelize": "^1.1.2", "loud-rejection": "^1.0.0", "map-obj": "^1.0.1", "minimist": "^1.1.3", "normalize-package-data": "^2.3.4", "object-assign": "^4.0.1", "read-pkg-up": "^1.0.1", "redent": "^1.0.0", "trim-newlines": "^1.0.0"}}, "methods": {"version": "1.1.2", "dev": true}, "mime": {"version": "1.6.0", "dev": true}, "mime-db": {"version": "1.40.0", "dev": true}, "mime-types": {"version": "2.1.24", "dev": true, "requires": {"mime-db": "1.40.0"}}, "mimeparse": {"version": "0.1.4", "dev": true}, "mimic-response": {"version": "2.1.0", "dev": true, "optional": true}, "minimatch": {"version": "3.0.4", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.0", "dev": true}, "mixin-deep": {"version": "1.3.2", "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.1", "dev": true, "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "dev": true}}}, "morgan": {"version": "1.9.1", "dev": true, "requires": {"basic-auth": "~2.0.0", "debug": "2.6.9", "depd": "~1.1.2", "on-finished": "~2.3.0", "on-headers": "~1.0.1"}}, "ms": {"version": "2.0.0"}, "multimatch": {"version": "4.0.0", "dev": true, "requires": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}}, "mute-stdout": {"version": "1.0.1"}, "nan": {"version": "1.0.0", "dev": true}, "nanomatch": {"version": "1.2.13", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"arr-diff": {"version": "4.0.0"}, "array-unique": {"version": "0.3.2"}, "kind-of": {"version": "6.0.2"}}}, "napi-build-utils": {"version": "1.0.1", "dev": true, "optional": true}, "negotiator": {"version": "0.6.2", "dev": true}, "netmask": {"version": "1.0.6", "dev": true}, "next-tick": {"version": "1.0.0"}, "ng-annotate": {"version": "1.2.2", "dev": true, "requires": {"acorn": "~2.6.4", "alter": "~0.2.0", "convert-source-map": "~1.1.2", "optimist": "~0.6.1", "ordered-ast-traverse": "~1.1.1", "simple-fmt": "~0.1.0", "simple-is": "~0.2.0", "source-map": "~0.5.3", "stable": "~0.1.5", "stringmap": "~0.2.2", "stringset": "~0.2.1", "tryor": "~0.1.2"}, "dependencies": {"source-map": {"version": "0.5.7", "dev": true}}}, "no-case": {"version": "2.3.2", "dev": true, "requires": {"lower-case": "^1.1.1"}}, "node-abi": {"version": "2.15.0", "dev": true, "optional": true, "requires": {"semver": "^5.4.1"}}, "node-http2": {"version": "4.0.1", "dev": true, "requires": {"assert": "1.4.1", "events": "1.1.1", "https-browserify": "0.0.1", "setimmediate": "^1.0.5", "stream-browserify": "2.0.1", "timers-browserify": "2.0.2", "url": "^0.11.0", "websocket-stream": "^5.0.1"}}, "node.extend": {"version": "2.0.2", "dev": true, "requires": {"has": "^1.0.3", "is": "^3.2.1"}}, "noop-logger": {"version": "0.1.1", "dev": true, "optional": true}, "nopt": {"version": "3.0.6", "dev": true, "requires": {"abbrev": "1"}}, "normalize-package-data": {"version": "2.4.0", "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "2.1.1", "requires": {"remove-trailing-separator": "^1.0.1"}}, "now-and-later": {"version": "2.0.1", "requires": {"once": "^1.3.2"}}, "npmlog": {"version": "4.1.2", "dev": true, "optional": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "nth-check": {"version": "1.0.1", "dev": true, "requires": {"boolbase": "~1.0.0"}}, "number-is-nan": {"version": "1.0.1"}, "oauth-sign": {"version": "0.9.0", "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "dev": true}, "object-component": {"version": "0.0.3", "dev": true}, "object-copy": {"version": "0.1.0", "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}}}, "object-keys": {"version": "1.0.11"}, "object-visit": {"version": "1.0.1", "requires": {"isobject": "^3.0.0"}, "dependencies": {"isobject": {"version": "3.0.1"}}}, "object.assign": {"version": "4.1.0", "requires": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}}, "object.defaults": {"version": "1.1.0", "requires": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}, "dependencies": {"array-slice": {"version": "1.1.0"}, "for-own": {"version": "1.0.0", "requires": {"for-in": "^1.0.1"}}, "isobject": {"version": "3.0.1"}}}, "object.map": {"version": "1.0.1", "requires": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "dependencies": {"for-own": {"version": "1.0.0", "requires": {"for-in": "^1.0.1"}}}}, "object.pick": {"version": "1.3.0", "requires": {"isobject": "^3.0.1"}, "dependencies": {"isobject": {"version": "3.0.1"}}}, "object.reduce": {"version": "1.0.1", "requires": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "dependencies": {"for-own": {"version": "1.0.0", "requires": {"for-in": "^1.0.1"}}}}, "on-finished": {"version": "2.3.0", "dev": true, "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2", "dev": true}, "once": {"version": "1.4.0", "requires": {"wrappy": "1"}}, "opn": {"version": "5.5.0", "dev": true, "requires": {"is-wsl": "^1.1.0"}}, "optimist": {"version": "0.6.1", "dev": true, "requires": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}, "dependencies": {"minimist": {"version": "0.0.10", "dev": true}}}, "optionator": {"version": "0.8.2", "dev": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.4", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "wordwrap": "~1.0.0"}, "dependencies": {"wordwrap": {"version": "1.0.0", "dev": true}}}, "options": {"version": "0.0.6", "dev": true}, "ordered-ast-traverse": {"version": "1.1.1", "dev": true, "requires": {"ordered-esprima-props": "~1.1.0"}}, "ordered-esprima-props": {"version": "1.1.0", "dev": true}, "ordered-read-streams": {"version": "1.0.1", "requires": {"readable-stream": "^2.0.1"}}, "os-locale": {"version": "1.4.0", "requires": {"lcid": "^1.0.0"}}, "os-tmpdir": {"version": "1.0.2", "dev": true}, "p-limit": {"version": "2.2.2", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.2.0", "dev": true}, "pac-proxy-agent": {"version": "2.0.2", "dev": true, "requires": {"agent-base": "^4.2.0", "debug": "^3.1.0", "get-uri": "^2.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "pac-resolver": "^3.0.0", "raw-body": "^2.2.0", "socks-proxy-agent": "^3.0.0"}, "dependencies": {"bytes": {"version": "3.1.0", "dev": true}, "debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "http-errors": {"version": "1.7.3", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "inherits": {"version": "2.0.4", "dev": true}, "ms": {"version": "2.1.2", "dev": true}, "raw-body": {"version": "2.4.1", "dev": true, "requires": {"bytes": "3.1.0", "http-errors": "1.7.3", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "setprototypeof": {"version": "1.1.1", "dev": true}}}, "pac-resolver": {"version": "3.0.0", "dev": true, "requires": {"co": "^4.6.0", "degenerator": "^1.0.4", "ip": "^1.1.5", "netmask": "^1.0.6", "thunkify": "^2.1.2"}}, "param-case": {"version": "2.1.1", "dev": true, "requires": {"no-case": "^2.2.0"}}, "parse-filepath": {"version": "1.0.2", "requires": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}}, "parse-json": {"version": "2.2.0", "requires": {"error-ex": "^1.2.0"}}, "parse-node-version": {"version": "1.0.1"}, "parse-passwd": {"version": "1.0.0"}, "parseqs": {"version": "0.0.5", "dev": true, "requires": {"better-assert": "~1.0.0"}}, "parseuri": {"version": "0.0.5", "dev": true, "requires": {"better-assert": "~1.0.0"}}, "parseurl": {"version": "1.3.3", "dev": true}, "pascalcase": {"version": "0.1.1"}, "path-dirname": {"version": "1.0.2"}, "path-exists": {"version": "2.1.0", "requires": {"pinkie-promise": "^2.0.0"}}, "path-is-absolute": {"version": "1.0.1"}, "path-parse": {"version": "1.0.6"}, "path-root": {"version": "0.1.1", "requires": {"path-root-regex": "^0.1.0"}}, "path-root-regex": {"version": "0.1.2"}, "path-type": {"version": "1.1.0", "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "pend": {"version": "1.2.0", "dev": true}, "performance-now": {"version": "2.1.0", "dev": true, "optional": true}, "picomatch": {"version": "2.2.1", "dev": true}, "pify": {"version": "2.3.0"}, "pinkie": {"version": "2.0.4"}, "pinkie-promise": {"version": "2.0.1", "requires": {"pinkie": "^2.0.0"}}, "pkg-up": {"version": "3.1.0", "dev": true, "requires": {"find-up": "^3.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "dev": true, "requires": {"locate-path": "^3.0.0"}}}}, "plur": {"version": "2.1.2", "dev": true, "requires": {"irregular-plurals": "^1.0.0"}}, "portscanner": {"version": "2.2.0", "dev": true, "requires": {"async": "^2.6.0", "is-number-like": "^1.0.3"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}}, "lodash": {"version": "4.17.15", "dev": true}}}, "posix-character-classes": {"version": "0.1.1"}, "prebuild-install": {"version": "5.3.3", "dev": true, "optional": true, "requires": {"detect-libc": "^1.0.3", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "napi-build-utils": "^1.0.1", "node-abi": "^2.7.0", "noop-logger": "^0.1.1", "npmlog": "^4.0.1", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^3.0.3", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0"}, "dependencies": {"pump": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "prelude-ls": {"version": "1.1.2", "dev": true}, "pretty-bytes": {"version": "4.0.2", "dev": true}, "pretty-hrtime": {"version": "1.0.3"}, "process-nextick-args": {"version": "2.0.0"}, "progress": {"version": "2.0.3", "dev": true}, "promise": {"version": "7.3.1", "dev": true, "optional": true, "requires": {"asap": "~2.0.3"}}, "proxy-agent": {"version": "2.3.1", "dev": true, "requires": {"agent-base": "^4.2.0", "debug": "^3.1.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "lru-cache": "^4.1.2", "pac-proxy-agent": "^2.0.1", "proxy-from-env": "^1.0.0", "socks-proxy-agent": "^3.0.0"}, "dependencies": {"debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "proxy-from-env": {"version": "1.0.0", "dev": true}, "prr": {"version": "1.0.1", "dev": true, "optional": true}, "pseudomap": {"version": "1.0.2", "dev": true}, "psl": {"version": "1.3.0", "dev": true, "optional": true}, "pump": {"version": "2.0.1", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "punycode": {"version": "2.1.1", "dev": true, "optional": true}, "puppeteer": {"version": "2.1.1", "dev": true, "requires": {"@types/mime-types": "^2.1.0", "debug": "^4.1.0", "extract-zip": "^1.6.6", "https-proxy-agent": "^4.0.0", "mime": "^2.0.3", "mime-types": "^2.1.25", "progress": "^2.0.1", "proxy-from-env": "^1.0.0", "rimraf": "^2.6.1", "ws": "^6.1.0"}, "dependencies": {"agent-base": {"version": "5.1.1", "dev": true}, "debug": {"version": "4.1.1", "dev": true, "requires": {"ms": "^2.1.1"}}, "glob": {"version": "7.1.6", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "https-proxy-agent": {"version": "4.0.0", "dev": true, "requires": {"agent-base": "5", "debug": "4"}}, "mime": {"version": "2.4.4", "dev": true}, "mime-db": {"version": "1.43.0", "dev": true}, "mime-types": {"version": "2.1.26", "dev": true, "requires": {"mime-db": "1.43.0"}}, "ms": {"version": "2.1.2", "dev": true}, "rimraf": {"version": "2.7.1", "dev": true, "requires": {"glob": "^7.1.3"}}, "ws": {"version": "6.2.1", "dev": true, "requires": {"async-limiter": "~1.0.0"}}}}, "q": {"version": "0.9.3", "dev": true}, "q-io": {"version": "1.13.6", "dev": true, "requires": {"es6-set": "^0.1.1", "mime": "^1.2.11", "mimeparse": "^0.1.4", "q": "^1.0.1", "qs": "^1.2.1", "url2": "^0.0.0"}, "dependencies": {"q": {"version": "1.5.1", "dev": true}, "qs": {"version": "1.2.2", "dev": true}}}, "qjobs": {"version": "1.2.0", "dev": true}, "qs": {"version": "6.5.2", "dev": true}, "querystring": {"version": "0.2.0", "dev": true}, "range-parser": {"version": "1.2.1", "dev": true}, "raw-body": {"version": "1.1.7", "dev": true, "requires": {"bytes": "1", "string_decoder": "0.10"}, "dependencies": {"string_decoder": {"version": "0.10.31", "dev": true}}}, "rc": {"version": "1.2.8", "dev": true, "optional": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}}, "read-pkg": {"version": "1.1.0", "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}}, "readable-stream": {"version": "2.3.6", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "3.3.0", "dev": true, "requires": {"picomatch": "^2.0.7"}}, "rechoir": {"version": "0.6.2", "requires": {"resolve": "^1.1.6"}}, "redent": {"version": "1.0.0", "dev": true, "requires": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}}, "regex-not": {"version": "1.0.2", "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "relateurl": {"version": "0.2.7", "dev": true}, "remove-bom-buffer": {"version": "3.0.0", "requires": {"is-buffer": "^1.1.5", "is-utf8": "^0.2.1"}}, "remove-bom-stream": {"version": "1.2.0", "requires": {"remove-bom-buffer": "^3.0.0", "safe-buffer": "^5.1.0", "through2": "^2.0.3"}}, "remove-trailing-separator": {"version": "1.1.0"}, "repeat-element": {"version": "1.1.2"}, "repeat-string": {"version": "1.6.1"}, "repeating": {"version": "2.0.1", "dev": true, "requires": {"is-finite": "^1.0.0"}}, "replace-ext": {"version": "1.0.0"}, "replace-homedir": {"version": "1.0.0", "requires": {"homedir-polyfill": "^1.0.1", "is-absolute": "^1.0.0", "remove-trailing-separator": "^1.1.0"}}, "request": {"version": "2.88.0", "dev": true, "optional": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "dependencies": {"extend": {"version": "3.0.2", "dev": true, "optional": true}}}, "require-directory": {"version": "2.1.1"}, "require-main-filename": {"version": "1.0.1"}, "requirefresh": {"version": "2.3.0", "dev": true, "requires": {"editions": "^2.2.0"}, "dependencies": {"editions": {"version": "2.3.0", "dev": true, "requires": {"errlop": "^2.0.0", "semver": "^6.3.0"}}, "semver": {"version": "6.3.0", "dev": true}}}, "requires-port": {"version": "1.0.0", "dev": true}, "resolve": {"version": "1.1.7"}, "resolve-dir": {"version": "1.0.1", "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}}, "resolve-from": {"version": "5.0.0", "dev": true}, "resolve-options": {"version": "1.1.0", "requires": {"value-or-function": "^3.0.0"}}, "resolve-pkg": {"version": "2.0.0", "dev": true, "requires": {"resolve-from": "^5.0.0"}}, "resolve-url": {"version": "0.2.1"}, "ret": {"version": "0.1.15"}, "rfdc": {"version": "1.1.4", "dev": true}, "rimraf": {"version": "3.0.2", "dev": true, "requires": {"glob": "^7.1.3"}, "dependencies": {"glob": {"version": "7.1.6", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "rsvp": {"version": "3.6.2", "dev": true}, "safe-buffer": {"version": "5.1.2"}, "safe-json-parse": {"version": "1.0.1", "dev": true}, "safe-regex": {"version": "1.1.0", "requires": {"ret": "~0.1.10"}}, "safefs": {"version": "4.2.0", "dev": true, "requires": {"editions": "^2.2.0", "graceful-fs": "^4.2.3"}, "dependencies": {"editions": {"version": "2.3.0", "dev": true, "requires": {"errlop": "^2.0.0", "semver": "^6.3.0"}}, "graceful-fs": {"version": "4.2.3", "dev": true}, "semver": {"version": "6.3.0", "dev": true}}}, "safer-buffer": {"version": "2.1.2", "dev": true}, "semver": {"version": "5.5.0"}, "semver-greatest-satisfied-range": {"version": "1.1.0", "requires": {"sver-compat": "^1.5.0"}}, "send": {"version": "0.17.1", "dev": true, "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "dependencies": {"http-errors": {"version": "1.7.3", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "inherits": {"version": "2.0.4", "dev": true}, "ms": {"version": "2.1.1", "dev": true}, "setprototypeof": {"version": "1.1.1", "dev": true}}}, "serve-index": {"version": "1.9.1", "dev": true, "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}}, "serve-static": {"version": "1.14.1", "dev": true, "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}}, "set-blocking": {"version": "2.0.0"}, "set-value": {"version": "2.0.1", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "dev": true}, "setprototypeof": {"version": "1.1.0", "dev": true}, "shelljs": {"version": "0.3.0", "dev": true}, "signal-exit": {"version": "3.0.2", "dev": true}, "simple-concat": {"version": "1.0.0", "dev": true, "optional": true}, "simple-fmt": {"version": "0.1.0", "dev": true}, "simple-get": {"version": "3.1.0", "dev": true, "optional": true, "requires": {"decompress-response": "^4.2.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "simple-is": {"version": "0.2.0", "dev": true}, "smart-buffer": {"version": "1.1.15", "dev": true}, "snapdragon": {"version": "0.8.2", "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "requires": {"is-extendable": "^0.1.0"}}, "source-map": {"version": "0.5.7"}}}, "snapdragon-node": {"version": "2.1.1", "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "isobject": {"version": "3.0.1"}, "kind-of": {"version": "6.0.2"}}}, "snapdragon-util": {"version": "3.0.1", "requires": {"kind-of": "^3.2.0"}}, "socket.io": {"version": "2.1.1", "dev": true, "requires": {"debug": "~3.1.0", "engine.io": "~3.2.0", "has-binary2": "~1.0.2", "socket.io-adapter": "~1.1.0", "socket.io-client": "2.1.1", "socket.io-parser": "~3.2.0"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}}}, "socket.io-adapter": {"version": "1.1.2", "dev": true}, "socket.io-client": {"version": "2.1.1", "dev": true, "requires": {"backo2": "1.0.2", "base64-arraybuffer": "0.1.5", "component-bind": "1.0.0", "component-emitter": "1.2.1", "debug": "~3.1.0", "engine.io-client": "~3.2.0", "has-binary2": "~1.0.2", "has-cors": "1.1.0", "indexof": "0.0.1", "object-component": "0.0.3", "parseqs": "0.0.5", "parseuri": "0.0.5", "socket.io-parser": "~3.2.0", "to-array": "0.1.4"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}}}, "socket.io-parser": {"version": "3.2.0", "dev": true, "requires": {"component-emitter": "1.2.1", "debug": "~3.1.0", "isarray": "2.0.1"}, "dependencies": {"debug": {"version": "3.1.0", "dev": true, "requires": {"ms": "2.0.0"}}, "isarray": {"version": "2.0.1", "dev": true}}}, "socks": {"version": "1.1.10", "dev": true, "requires": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}}, "socks-proxy-agent": {"version": "3.0.1", "dev": true, "requires": {"agent-base": "^4.1.0", "socks": "^1.1.10"}}, "source-map": {"version": "0.6.1", "dev": true}, "source-map-resolve": {"version": "0.5.2", "requires": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.2.10", "dev": true, "requires": {"source-map": "0.1.32"}, "dependencies": {"source-map": {"version": "0.1.32", "dev": true, "requires": {"amdefine": ">=0.0.4"}}}}, "source-map-url": {"version": "0.4.0"}, "sparkles": {"version": "1.0.1"}, "spdx-correct": {"version": "3.0.0", "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.1.0"}, "spdx-expression-parse": {"version": "3.0.0", "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.0"}, "split-string": {"version": "3.1.0", "requires": {"extend-shallow": "^3.0.0"}}, "sprintf-js": {"version": "1.0.3", "dev": true}, "sshpk": {"version": "1.16.1", "dev": true, "optional": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "stable": {"version": "0.1.8", "dev": true}, "stack-trace": {"version": "0.0.10"}, "static-extend": {"version": "0.1.2", "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "requires": {"is-descriptor": "^0.1.0"}}}}, "statuses": {"version": "1.5.0", "dev": true}, "stream-browserify": {"version": "2.0.1", "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-buffers": {"version": "2.2.0", "dev": true}, "stream-exhaust": {"version": "1.0.2"}, "stream-shift": {"version": "1.0.0"}, "streamqueue": {"version": "1.1.2", "dev": true, "requires": {"isstream": "^0.1.2", "readable-stream": "^2.3.3"}}, "streamroller": {"version": "1.0.6", "dev": true, "requires": {"async": "^2.6.2", "date-format": "^2.0.0", "debug": "^3.2.6", "fs-extra": "^7.0.1", "lodash": "^4.17.14"}, "dependencies": {"async": {"version": "2.6.3", "dev": true, "requires": {"lodash": "^4.17.14"}}, "debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "fs-extra": {"version": "7.0.1", "dev": true, "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "lodash": {"version": "4.17.15", "dev": true}, "ms": {"version": "2.1.2", "dev": true}}}, "string_decoder": {"version": "1.1.1", "requires": {"safe-buffer": "~5.1.0"}}, "string-length": {"version": "1.0.1", "dev": true, "requires": {"strip-ansi": "^3.0.0"}}, "string-template": {"version": "0.2.1", "dev": true}, "string-width": {"version": "1.0.2", "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "string.prototype.endswith": {"version": "0.2.0", "dev": true}, "stringmap": {"version": "0.2.2", "dev": true}, "stringset": {"version": "0.2.1", "dev": true}, "strip-ansi": {"version": "3.0.1", "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "2.0.0", "requires": {"is-utf8": "^0.2.0"}}, "strip-indent": {"version": "1.0.1", "dev": true, "requires": {"get-stdin": "^4.0.1"}}, "strip-json-comments": {"version": "2.0.1", "dev": true, "optional": true}, "superagent": {"version": "3.8.3", "dev": true, "requires": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}, "dependencies": {"debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "superagent-proxy": {"version": "1.0.3", "dev": true, "requires": {"debug": "^3.1.0", "proxy-agent": "2"}, "dependencies": {"debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "supports-color": {"version": "2.0.0", "dev": true}, "sver-compat": {"version": "1.5.0", "requires": {"es6-iterator": "^2.0.1", "es6-symbol": "^3.1.1"}}, "tar-fs": {"version": "2.0.0", "dev": true, "optional": true, "requires": {"chownr": "^1.1.1", "mkdirp": "^0.5.1", "pump": "^3.0.0", "tar-stream": "^2.0.0"}, "dependencies": {"bl": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"readable-stream": "^3.0.1"}}, "pump": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "readable-stream": {"version": "3.6.0", "dev": true, "optional": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "tar-stream": {"version": "2.1.0", "dev": true, "optional": true, "requires": {"bl": "^3.0.0", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}}}}, "tar-stream": {"version": "1.6.2", "dev": true, "requires": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}}, "text-table": {"version": "0.2.0", "dev": true}, "through2": {"version": "2.0.3", "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "through2-filter": {"version": "3.0.0", "requires": {"through2": "~2.0.0", "xtend": "~4.0.0"}}, "thunkify": {"version": "2.1.2", "dev": true}, "time-stamp": {"version": "1.1.0"}, "timers-browserify": {"version": "2.0.2", "dev": true, "requires": {"setimmediate": "^1.0.4"}}, "tiny-lr": {"version": "1.1.1", "dev": true, "requires": {"body": "^5.1.0", "debug": "^3.1.0", "faye-websocket": "~0.10.0", "livereload-js": "^2.3.0", "object-assign": "^4.1.0", "qs": "^6.4.0"}, "dependencies": {"debug": {"version": "3.2.6", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "dev": true}}}, "tinycolor": {"version": "0.0.1", "dev": true}, "tmp": {"version": "0.0.33", "dev": true, "requires": {"os-tmpdir": "~1.0.2"}}, "to-absolute-glob": {"version": "2.0.2", "requires": {"is-absolute": "^1.0.0", "is-negated-glob": "^1.0.0"}}, "to-array": {"version": "0.1.4", "dev": true}, "to-buffer": {"version": "1.1.1", "dev": true}, "to-object-path": {"version": "0.3.0", "requires": {"kind-of": "^3.0.2"}}, "to-regex": {"version": "3.0.2", "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "dependencies": {"is-number": {"version": "3.0.0", "requires": {"kind-of": "^3.0.2"}}}}, "to-through": {"version": "2.0.0", "requires": {"through2": "^2.0.3"}}, "toidentifier": {"version": "1.0.0", "dev": true}, "tough-cookie": {"version": "2.4.3", "dev": true, "optional": true, "requires": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "dev": true, "optional": true}}}, "traceur": {"version": "0.0.111", "dev": true, "requires": {"commander": "2.9.x", "glob": "5.0.x", "rsvp": "^3.0.13", "semver": "^4.3.3", "source-map-support": "~0.2.8"}, "dependencies": {"glob": {"version": "5.0.15", "dev": true, "requires": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "semver": {"version": "4.3.6", "dev": true}}}, "trim-newlines": {"version": "1.0.0", "dev": true}, "tryor": {"version": "0.1.2", "dev": true}, "tunnel-agent": {"version": "0.6.0", "dev": true, "optional": true, "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "dev": true, "optional": true}, "type": {"version": "1.0.3"}, "type-check": {"version": "0.3.2", "dev": true, "requires": {"prelude-ls": "~1.1.2"}}, "type-is": {"version": "1.6.18", "dev": true, "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typechecker": {"version": "4.11.0", "dev": true, "requires": {"editions": "^2.2.0"}, "dependencies": {"editions": {"version": "2.3.0", "dev": true, "requires": {"errlop": "^2.0.0", "semver": "^6.3.0"}}, "semver": {"version": "6.3.0", "dev": true}}}, "typedarray": {"version": "0.0.6"}, "uglify-js": {"version": "3.6.0", "dev": true, "requires": {"commander": "~2.20.0", "source-map": "~0.6.1"}, "dependencies": {"commander": {"version": "2.20.0", "dev": true}}}, "ultron": {"version": "1.1.1", "dev": true}, "unc-path-regex": {"version": "0.1.2"}, "underscore": {"version": "1.4.4", "dev": true}, "underscore.string": {"version": "3.3.5", "dev": true, "requires": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}}, "undertaker": {"version": "1.2.1", "requires": {"arr-flatten": "^1.0.1", "arr-map": "^2.0.0", "bach": "^1.0.0", "collection-map": "^1.0.0", "es6-weak-map": "^2.0.1", "last-run": "^1.1.0", "object.defaults": "^1.0.0", "object.reduce": "^1.0.0", "undertaker-registry": "^1.0.0"}}, "undertaker-registry": {"version": "1.0.1"}, "union-value": {"version": "1.0.1", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "unique-stream": {"version": "2.3.1", "requires": {"json-stable-stringify-without-jsonify": "^1.0.1", "through2-filter": "^3.0.0"}}, "universalify": {"version": "0.1.2", "dev": true}, "unpipe": {"version": "1.0.0", "dev": true}, "unset-value": {"version": "1.0.0", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4"}, "isobject": {"version": "3.0.1"}}}, "upath": {"version": "1.1.2"}, "upper-case": {"version": "1.1.3", "dev": true}, "uri-js": {"version": "4.2.2", "dev": true, "optional": true, "requires": {"punycode": "^2.1.0"}}, "uri-path": {"version": "1.0.0", "dev": true}, "urix": {"version": "0.1.0"}, "url": {"version": "0.11.0", "dev": true, "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "dev": true}}}, "url-safe": {"version": "2.0.0", "dev": true}, "url2": {"version": "0.0.0", "dev": true}, "use": {"version": "3.1.1"}, "useragent": {"version": "2.3.0", "dev": true, "requires": {"lru-cache": "4.1.x", "tmp": "0.0.x"}}, "util": {"version": "0.10.3", "dev": true, "requires": {"inherits": "2.0.1"}, "dependencies": {"inherits": {"version": "2.0.1", "dev": true}}}, "util-deprecate": {"version": "1.0.2"}, "utils-merge": {"version": "1.0.1", "dev": true}, "uuid": {"version": "3.3.3", "dev": true, "optional": true}, "v8flags": {"version": "3.1.3", "requires": {"homedir-polyfill": "^1.0.1"}}, "validate-npm-package-license": {"version": "3.0.3", "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "value-or-function": {"version": "3.0.0"}, "verror": {"version": "1.10.0", "dev": true, "optional": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "vinyl": {"version": "2.2.0", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}}, "vinyl-fs": {"version": "3.0.3", "requires": {"fs-mkdirp-stream": "^1.0.0", "glob-stream": "^6.1.0", "graceful-fs": "^4.0.0", "is-valid-glob": "^1.0.0", "lazystream": "^1.0.0", "lead": "^1.0.0", "object.assign": "^4.0.4", "pumpify": "^1.3.5", "readable-stream": "^2.3.3", "remove-bom-buffer": "^3.0.0", "remove-bom-stream": "^1.2.0", "resolve-options": "^1.1.0", "through2": "^2.0.0", "to-through": "^2.0.0", "value-or-function": "^3.0.0", "vinyl": "^2.0.0", "vinyl-sourcemap": "^1.1.0"}}, "vinyl-sourcemap": {"version": "1.1.0", "requires": {"append-buffer": "^1.0.2", "convert-source-map": "^1.5.0", "graceful-fs": "^4.1.6", "normalize-path": "^2.1.1", "now-and-later": "^2.0.0", "remove-bom-buffer": "^3.0.0", "vinyl": "^2.0.0"}, "dependencies": {"convert-source-map": {"version": "1.6.0", "requires": {"safe-buffer": "~5.1.1"}}}}, "void-elements": {"version": "2.0.1", "dev": true}, "w3cjs": {"version": "0.4.0", "dev": true, "requires": {"commander": "^2.9.0", "superagent": "^3.5.2", "superagent-proxy": "^1.0.2"}}, "walkdir": {"version": "0.0.11", "dev": true}, "websocket-driver": {"version": "0.7.3", "dev": true, "requires": {"http-parser-js": ">=0.4.0 <0.4.11", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4", "dev": true}, "websocket-stream": {"version": "5.5.0", "dev": true, "requires": {"duplexify": "^3.5.1", "inherits": "^2.0.1", "readable-stream": "^2.3.3", "safe-buffer": "^5.1.2", "ws": "^3.2.0", "xtend": "^4.0.0"}, "dependencies": {"ws": {"version": "3.3.3", "dev": true, "requires": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}}}, "which": {"version": "1.2.14", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "1.0.0"}, "which-pm-runs": {"version": "1.0.0", "dev": true, "optional": true}, "wide-align": {"version": "1.1.3", "dev": true, "optional": true, "requires": {"string-width": "^1.0.2 || 2"}}, "wordwrap": {"version": "0.0.2", "dev": true}, "wrap-ansi": {"version": "2.1.0", "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}}, "wrappy": {"version": "1.0.2"}, "ws": {"version": "0.4.32", "dev": true, "requires": {"commander": "~2.1.0", "nan": "~1.0.0", "options": ">=0.0.5", "tinycolor": "0.x"}, "dependencies": {"commander": {"version": "2.1.0", "dev": true}}}, "xmlbuilder": {"version": "9.0.7", "dev": true}, "xmlhttprequest-ssl": {"version": "1.5.5", "dev": true}, "xregexp": {"version": "2.0.0", "dev": true}, "xtend": {"version": "4.0.1"}, "y18n": {"version": "3.2.1"}, "yallist": {"version": "2.1.2", "dev": true}, "yargs-parser": {"version": "5.0.0", "requires": {"camelcase": "^3.0.0"}, "dependencies": {"camelcase": {"version": "3.0.0"}}}, "yauzl": {"version": "2.4.1", "dev": true, "requires": {"fd-slicer": "~1.0.1"}}, "yeast": {"version": "0.1.2", "dev": true}, "zip-stream": {"version": "1.2.0", "dev": true, "requires": {"archiver-utils": "^1.3.0", "compress-commons": "^1.2.0", "lodash": "^4.8.0", "readable-stream": "^2.0.0"}}}}