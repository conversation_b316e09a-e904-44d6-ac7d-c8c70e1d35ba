{"Change Plan": "变更计划", "Change Password": "修改密码", "Logout": "退出", "Exceptions": "异常情况", "Log Messages": "日志消息", "Broken Links": "失效链接", "Feature Usages": "特性使用", "All": "全部日志", "Reports": "报表反馈", "Exception Events": "例外时间表", "Message Events": "消息时间表", "Broken Links Events": "链接断开时间轴", "Feature Events": "功能时间表", "Event Events": "活动时间表", "Most Frequent Exceptions": "高频异常", "Most Frequent Messages": "高频消息", "Most Frequent Links": "高频死链", "Most Frequent Features": "高频特性", "Most Frequent": "高频日志", "Most Frequent Stacks": "最频繁的堆栈", "Most Users": "用户最多", "Most Users Stacks": "大多数用户堆栈", "New Exception": "最新异常", "New LogMessage": "最新消息", "New BrokenLink": "最新死链", "New FeatureUsage": "最新特性", "New Event": "最新日志", "Sessions": "会话量", "Project Manager": "项目管理", "Organization Manager": "组织管理", "My Account": "我的帐户", "Documentation": "说明文档", "Support": "在线支持", "New Stacks": "最新堆栈数", "Events Per Hour": "事件数每小时", "History": "历史", "Session": "会话", "Session End": "会话结束", "Session Heartbeat": "会话心跳", "Feature": "特性", "Go To Documentation": "前往说明文档", "Focus Search Bar": "激活搜索框", "Go To My Account": "前往我的账户", "Go To Notifications": "前往通知视图", "Go To Organizations": "前往组织视图", "Go To Projects": "前往项目视图", "Go To GitHub project": "前往 GitHub 项目", "Go to public Discord channel": "前往 Discord 公共频道", "Chat with Support": "联系客服", "Your user account was deleted. Please create a new account.": "您的账号已经被删除。请创建新账号。", "Billing is currently disabled.": "当前系统不支持在线支付。", "Page Not Found": "未找到页面", "We're sorry but the page was not found on this site.": "很抱歉，在此网站中没有找到您要访问的页面。", "Service Status": "服务状态", "We're sorry but the website is currently undergoing maintenance.": "很抱歉，此网站目前正在进行维护。", "You'll be automatically redirected when the maintenance is completed.": "在维护完成后，系统将自动重定向到您要访问的页面。", "Please enter a Reference Link": "请输入参考链接", "Reference Link": "参考链接", "Please enter a valid URL": "请输入有效网址", "Reference Link is required.": "参考链接是必需的。", "Save Reference Link": "保存参考链接", "UserRatio": "用户比例", "First": "首次", "Last": "末次", "Stack": "堆栈", "Options": "选项", "Hide": "隐藏", "Future Occurrences Are Critical": "此堆栈后续的事件标记为关键事件", "Disable Notifications": "禁止通知", "Promote To External": "外部推送", "Add Reference Link": "新增参考链接", "Title": "标题", "Project": "项目", "Date Fixed": "修复日期", "Description": "说明", "Tags": "标签", "Stacking Information": "堆栈信息", "ExceptionType": "异常类型", "Method": "方法", "Chart Options": "图表选项", "Show Average Value": "显示平均值", "Average Value": "平均值", "Show Value Sum": "显示合计值", "Value Sum": "合计值", "Recent Occurrences": "最近发生", "Reference Links": "参考链接", "Mark Fixed": "标记已修复", "No stacks were found with the current filter.": "未找到满足当前筛选条件的堆栈。", "No stacks were found.": "尚未发现任何堆栈。", "Future Stack Occurrences are Not Critical": "此堆栈后续的事件为非关键事件", "Future Stack Occurrences are Critical": "此堆栈后续的事件为关键事件", "Enable Stack Notifications": "启用堆栈通知", "Disable Stack Notifications": "禁用堆栈通知", "Promote Stack To External": "推送此堆栈到外部系统", "Add Stack Reference Link": "新增堆栈参考链接", "Delete Stack": "删除堆栈", "An error occurred while adding the reference link.": "在新增参考链接的过程中发生了错误。", "Successfully promoted stack!": "成功将堆栈推送到外部系统！", "Manage Integrations": "管理外部整合", "An error occurred while promoting this stack.": "在将堆栈推送至外部系统的过程中发生了错误。", "Are you sure you want to delete this reference link?": "您确认要删除此参考链接？", "An error occurred while deleting the external reference link.": "在删除外部参考链接的过程中发生了错误。", "Are you sure you want to delete this stack (includes all stack events)?": "您确认要删除此堆栈及其所有日志吗？", "Successfully queued the stack for deletion.": "已将此堆栈放入队列等待删除。", "An error occurred while deleting this stack.": "在删除堆栈的过程中发生了错误。", "The average of all event values": "所有事件的平均值", "The sum of all event values": "所有事件值的总和", "Hide this stack from reports and mutes occurrence notifications": "在报表和事件通知中隐藏此堆栈", "All future occurrences will be marked as critical": "后续的事件将会被标记为关键事件", "Stop sending occurrence notifications for this stack": "停止发送此堆栈的通知", "Used to promote stacks to external systems.": "用于将堆栈推送到外部系统。", "Add a reference link to an external resource.": "为外部资源增加引用链接。", "Delete this stack": "删除堆栈", "Summary": "概要", "Created": "已创建", "Duration": "持续时间", "Loading...": "加载中...", "Users": "用户量", "Average Duration": "平均持续时间", "Sessions and Users": "用户会话", "View Active": "查看活动会话", "Recent Sessions": "近期会话", "No sessions were found with the current filter.": "未找到满足当前筛选条件的会话。", "No sessions were found.": "尚未发现任何会话。", "Occurred On": "发生时间", "ended": "结束于", "Reference": "参考", "Level": "日志等级", "Event Type": "事件类型", "Error Type": "错误类型", "Source": "来源", "Value": "值", "Message": "消息", "Version": "版本", "Geo": "地理位置", "URL": "请求URL", "User Info": "用户信息", "User Email": "用户邮箱", "User Identity": "用户标识", "User Name": "用户名", "User Description": "用户说明", "Stack Trace": "堆栈跟踪", "Machine Name": "计算机名", "IP Address": "IP地址", "Processor Count": "处理器核数", "Total Memory": "总内存", "Available Memory": "可用内存", "Process Memory": "已用内存", "OS Name": "操作系统", "OS Version": "系统版本", "Architecture": "系统架构", "Runtime Version": "运行时版本", "Process ID": "进程标识", "Process Name": "进程名称", "Command Line": "命令行", "Additional Data": "附加数据", "Code": "错误编码", "Submission Method": "提交方法", "Loaded Modules": "已加载模块", "Module Name": "模块名称", "HTTP Method": "HTTP请求方法", "Referrer": "访问来源", "Client IP": "客户端IP", "User Agent": "User Agent", "Device": "设备", "Browser": "浏览器", "Browser OS": "浏览器系统", "Post Data": "提交数据", "Session Events": "会话事件", "Event Occurrence": "发生事件", "Go To Stack": "前往堆栈", "Previous Occurrence": "上个事件", "Next Occurrence": "下个事件", "Toggle View": "切换视图", "Promote to Tab": "提升为标签", "Demote Tab": "降级标签", "References": "参考", "Overview": "概要", "Exception": "异常", "Environment": "环境", "Extended Data": "扩展数据", "Request": "请求", "to enable sessions and other premium features!": "以启用会话及其他高级特性！", "is attempting to use a premium feature.": "正在尝试使用高级特性。", "View JSON": "查看 JSON", "Copy Stack Trace to Clipboard": "将堆栈跟踪复制到剪贴板", "Copy Event JSON to Clipboard": "将事件以JSON格式复制到剪贴板", "Copy to Clipboard": "复制到剪贴板", "Cookie Values": "<PERSON><PERSON>值", "No events were found with the current filter.": "未找到满足当前筛选条件的日志。", "No events were found.": "尚未发现任何日志。", "An error occurred promoting tab.": "提升标签的过程中发生了错误。", "Cannot_Find_Event": "未找到事件\"{{eventId}}\"。", "Online": "在线", "Seconds": "{{duration}}秒", "User": "用户", "Bulk Action": "批量操作", "Mark Hidden": "标记隐藏", "Sort Descending": "降序排序", "Sort Ascending": "升序排序", "Date": "日期", "Please select one or more events": "请至少选择一条日志", "Successfully queued the events for deletion.": "已将日志放入队列等待删除。", "An error occurred while deleting the events.": "在删除日志的过程中发生了错误。", "Are you sure you want to delete these events?": "您确认要删除这些日志吗？", "DELETE EVENTS": "删除日志", "Are you sure you want to delete these stacks (includes all stack events)?": "您确认要删除这些堆栈及其所有日志吗？", "DELETE STACKS": "删除堆栈", "An error occurred while deleting the stacks.": "在删除堆栈的过程中发生了错误。", "Successfully marked the stacks as fixed.": "成功将堆栈标记为固定。", "An error occurred while marking stacks as fixed.": "在将堆栈标记为已修复的过程中发生了错误。", "Successfully queued the stacks to be marked as hidden.": "已将堆栈放入队列等待被标记为隐藏。", "An error occurred while marking stacks as hidden.": "在将堆栈标记为隐藏的过程中发生了错误。", "Successfully queued the stacks for deletion.": "已将堆栈放入队列等待删除。", "Please select one or more stacks": "请至少选择一个堆栈", "Marks the stack as fixed. This will also prevent error occurrences from being displayed in the dashboard.": "标记此堆栈为已修复。此操作可以避免错误堆栈继续在概览视图显示。", "Optional:": "可选：", "Please enter the version in which the stack has been fixed. Any submitted occurrences with a lower version will not cause a regression.": "请输入此堆栈修复的版本号。任何低于此版本的应用程序所提交的可归类到此堆栈的日志不会被视为回归日志。", "Versioning Documentation": "版本化说明文档", "Optional Version (Example: 1.2.3)": "可选版本（例如：1.2.3）", "Invalid version.": "无效的版本号。", "Mark this stack as open": "将此堆栈标记为打开", "Mark this stack as fixed": "标记此堆栈为已修复", "Marking Not Fixed": "正在标记未修复", "Marking Fixed": "正在标记已修复", "Stack_Deleted": "堆栈\"{{stackId}}\"已被删除。", "Cannot_Find_Stack": "无法找到堆栈\"{{stackId}}\"。", "Error_Load_Stack": "在加载堆栈\"{{stackId}}\"的过程中发生了错误。", "An error occurred while marking this stack as open.": "将该堆栈标记为打开时发生错误。", "An error occurred while marking this stack as discarded.": "将该堆栈标记为已丢弃时发生错误。", "Are you sure you want to all current stack events and discard any future stack events?": "您确定要所有当前的堆栈事件并丢弃以后的所有堆栈事件吗？", "All future occurrences will be discarded and will not count against your event limit.": "将来所有出现的事件都将被丢弃，并且不会计入您的事件限制。", "An error occurred while marking future occurrences as not critical.": "在将后续事件标记为非关键事件的过程中发生了错误。", "An error occurred while marking future occurrences as critical.": "在将后续事件标记为关键事件的过程中发生了错误。", "Successfully marked the stack as open.": "成功将堆栈标记为打开。", "Successfully marked the stack as fixed.": "成功将堆栈标记为固定。", "Successfully marked the stacks as ignored.": "成功将堆栈标记为已忽略。", "An error occurred while marking this stack as fixed.": "在将队列标记为已修复的过程中发生了错误。", "An error occurred while marking this stack as snoozed.": "将此堆栈标记为已延后时发生错误。", "An error occurred while marking this stack as ignored.": "将该堆栈标记为已忽略时发生错误。", "An error occurred while marking stacks as ignored.": "将堆栈标记为忽略时发生错误。", "Successfully marked the stacks as open.": "成功将堆栈标记为打开。", "An error occurred while marking stacks as open.": "将堆栈标记为打开时发生错误。", "Successfully marked the stacks as discarded.": "成功将堆栈标记为已丢弃。", "An error occurred while marking stacks as discarded.": "将堆栈标记为已丢弃时发生错误。", "Mark Open": "标记堆栈打开", "Mark Stack Open": "标记堆栈打开", "Mark Stack Discarded": "标记堆栈已舍弃", "Mark Stack Fixed": "标记为已修复", "Fixed_With_Version": "在 {{fixed_in_version}} 版本中修复", "Opening": "开场", "Discard": "丢弃", "open": "打开", "discarded": "舍弃", "fixed": "已修复", "regressed": "回归", "snoozed": "延后", "ignored": "被忽略", "6 Hours": "6个小时", "1 Day": "1天", "1 Week": "1周", "1 Month": "1个月", "All Projects": "所有项目", "Loading Organizations...": "加载组织中...", "Loading Projects...": "加载项目中...", "Add New Project": "创建项目", "Last Hour": "最近 1 小时", "Last 24 Hours": "最近 1 天", "Last Week": "最近 7 天", "Last 30 Days": "最近 30 天", "All Time": "全部时间", "Custom": "自定义", "Select Date Range": "选择日期范围", "In Date Range": "在日期范围内", "DayFormat": "D日", "DateFormat": "YYYY年M月D日", "DateTimeFormat": "YYYY年M月D日 H:mm:ss", "Filter": "过滤条件", "Search Documentation": "说明文档", "My Projects": "我的项目", "Project Information": "项目信息", "Organization Name": "组织名称", "New Organization Name": "新组织名称", "Project Name": "项目名称", "New Project Name": "新项目名称", "Add Project": "创建项目", "Manage Project": "管理项目", "General": "常规", "API Keys": "API 密钥", "Settings": "设置", "Client Configuration": "客户端配置", "Integrations": "外部整合", "Download & Configure Client": "下载并配置客户端", "Manage Organization": "管理组织", "Manage Notification Settings": "管理通知设置", "Reset Project Data": "重置项目数据", "Delete Project": "删除项目", "API Key": "API 密钥", "Enable": "启用", "Disable": "禁用", "Are you sure you want to enable the API key?": "您确定要启用API密钥吗？", "ENABLE API KEY": "启用API密钥", "An error occurred while enabling the API key.": "启用API密钥时发生错误。", "Are you sure you want to disable the API key?": "您确定要禁用API密钥吗？", "DISABLE API KEY": "禁用API密钥", "An error occurred while disabling the API key.": "禁用API密钥时发生错误。", "Notes": "备注", "Payment Number": "付款编号", "Actions": "操作", "Status": "状态", "This project does not have an API Key.": "此项目还没有 API 秘钥。", "New API Key": "创建 API 密钥", "New Client Configuration": "添加客户端配置", "Event Types": "事件类型", "Add Web Hook": "新增 Web Hook", "Connect Zapier": "连接 Zapier", "Data Exclusions": "数据排除", "User Namespaces": "用户命名空间", "Error Stacking": "错误堆栈", "Common Methods": "常用方法", "Spam Detection": "垃圾邮件检测", "Please enter a configuration setting": "请输入客户端配置", "Name": "名称", "Organization": "组织", "Stacks": "堆栈数", "Events": "事件数", "View Organization": "查看组织", "Key": "键", "Project Name is required.": "请填写项目名称", "A project with this name already exists.": "已经存在相同名称的项目。", "This project does not contain any custom configuration settings.": "此项目不包含任何自定义配置参数。", "NOTICE_CONFIGURATION": "此页面中的<a href=\"https://exceptionless.com/docs/project-settings/#client-configuration\" target=\"_blank\">配置参数</a>将会被实时发送到 Exceptionless 客户端。这种方法可以免部署而动态改变您的应用程序的工作方式。", "No projects were found with the current filter.": "未找到满足当前筛选条件的项目。", "No projects were found.": "尚未发现任何项目。", "Notice_Data_Exclusions": "使用逗号分隔的字段名称列表用于移除任何错误报告中的相关数据（例如扩展数据属性、表单字段、<PERSON>ie 和查询参数）。 您也可以设置一个<a href=\"https://exceptionless.com/docs/security/\" target=\"_blank\" title=\"数据排除说明文档\">带通配符(<strong>*</strong>)的字段名称</a>匹配前缀、后缀或者包含，以提高安全性。", "Automatically remove user identifiable information from events (e.g., Machine Name, User Information, IP Addresses and more...).": "自动从事件中删除用户可识别信息（例如，机器名称，用户信息，IP地址等等", "Notice_Error_Stacking": "使用逗号分隔的命名空间列表，这些命名空间应该是您的应用程序代码中所定义的。在此值填写之后，只有在列表命名空间中的方法调用才会被包含在错误堆栈中。", "Notice_Common_Methods": "使用逗号分隔的常用方法名称，这些方法不应该被包含在错误堆栈中。如果在您的代码包含一些公用方法，此功能在这些方法抛出大量异常的情况下非常有价值。", "Notice_Spam_Detection": "使用逗号分隔的 HTTP 请求的 User Agent 列表，任何匹配列表中 User Agent 的请求都将会被忽略。", "Example:": "例如：", "Reduce noise by automatically hiding high volumes of events coming from a single client IP address.": "自动隐藏来自相同IP地址的高频事件以减少干扰。", "Please enter a valid key.": "请输入有效的键", "Please enter a valid value.": "请输入有效的值", "Configuration Key": "配置键", "Configuration Value": "配置值", "to enable integrations!": "以启用外部整合！", "Choose how often you want to receive slack notifications for event occurrences in this project.": "请选择您希望以何频率接收此项目中事件的Slack通知", "(Coming soon!)": "（即将推出！）", "Add Slack Notifications": "新增 Slack 通知", "Remove Slack": "移除 Slack", "Notice_Web_hooks": "此项目会调用下面表格中的Web Hook", "Notice_Zapier": "Exceptionless具有本地Zapier集成。 您可以使用Zapier将您的Exceptionless帐户连接到3,000多个其他应用程序，而无需编写任何代码。", "This project does not contain any integrations.": "此项目不包含任何外部整合。", "An error occurred while loading your projects.": "在加载您的项目的过程中发生了错误。", "Are you sure you want to delete this project?": "您确认要删除此项目？", "Successfully queued the project for deletion.": "已将此项目放入队列等待删除。", "An error occurred while trying to remove the project.": "在删除项目的过程中发生了错误。", "Download_Configure_Project": "下载配置项目{{projectName}}的客户端", "The Exceptionless client can be integrated into your project in just a few easy steps.": "通过以下几个简单的步骤就可以轻松地将 Exceptionless 客户端整合到您的项目中去。", "Select your project type:": "请选择您的项目类型：", "Please select a project type": "请选择项目类型", "Execute the following in your shell:": "在您的shell中执行以下命令：", "Add the script to your HTML page:": "将下面的脚本放到您的 HTML 页面中", "Configure the ExceptionlessClient with your Exceptionless API key:": "通过您的 API 密钥配置 ExceptionlessClient ：", "Notice_Install_Nuget_Package": "在 Visual Studio 中的<a href=\"http://docs.nuget.org/docs/start-here/using-the-package-manager-console\">程序包管理器控制台</a>运行如下命令可以将 Exceptionless 的 <a href=\"http://nuget.org\" target=\"_blank\">NuGet</a> 程序包安装到您的项目中：", "Notice_Install_Manually": "如果您希望手动安装或者从 CDN 引用，请仔细阅读 <a href=\"https://github.com/exceptionless/Exceptionless.JavaScript\" target=\"_blank\">详细说明</a>。", "Notice_Install_Node_Package": "通过 <a href=\"https://www.npmjs.com\" target=\"_blank\">Node 程序包管理器</a> 安装 Exceptionless ：", "Notice_Update_ApiKey": "在项目中的 {{config}} 文件中找到 <code>&lt;exceptionless apiKey=\"API_KEY_HERE\" /&gt;</code> 部分并使用您的 Exceptionless 相应项目的 API 密钥替换：", "Notice_Application_Startup_NetFx": "在您的应用程序的启动文件中引入 <code>Exceptionless</code> 命名空间，然后通过 API 密钥执行 <code>ExceptionlessClient.Default.Startup(\"{{apiKey}}\")</code> 方法。", "Notice_Application_Startup_NetCore": "导入 <code>Exceptionless</code> 命名空间并在 ConfigureServices 方法中调用 <code>services.AddExceptionless(\"{{apiKey}}\")</code> 方法。 接下来，在应用程序启动期间调用配置中的 <code>app.UseExceptionless()</code> 方法。", "Notice_Application_Startup_Nancy": "在您的 Nancy 引导程序中引入 <code>Exceptionless</code> 命名空间，然后在 ApplicationStartup 方法中通过 Nancy.Bootstrapper.IPipelines 类型的实例调用 <code>Exceptionless.ExceptionlessClient.Default.RegisterNancy(pipelines)</code> 方法。", "Notice_Application_Startup_Windows": "在您的应用程序的启动文件中引入 <code>Exceptionless</code> 命名空间， 然后执行 <code>ExceptionlessClient.Default.Register()</code> 方法。", "Notice_Application_Startup_WebApi": "在您的应用程序的启动文件中引入 <code>Exceptionless</code> 命名空间， 然后通过 HttpConfiguration 类型的实例调用 <code>ExceptionlessClient.Default.RegisterWebApi(config)</code> 方法。", "Notice_Hosting_WebApi_In_AspNet": "如果将 Web API 程序托管在 ASP.NET 应用程序中，您需要这样注册 Exceptionless : <code>Exceptionless.ExceptionlessClient.Default.RegisterWebApi(GlobalConfiguration.Configuration)</code>", "Notice_Unhandled_Exception": "如果您瞄准的是Net Standard或Portable Class Libraries（PCL），则可能需要连接到任何特定于您的项目类型的未处理异常事件，才能开始报告它们！请看 <a target=\"_blank\" href=\"https://exceptionless.com/docs/clients/dotnet/\">Exceptionless 文档</a>以获取更多的信息。", "That's it!": "就是这样！", "Your project should now automatically be sending all unhandled exceptions to Exceptionless!": "现在您的项目可以自动将所有未处理异常发送到 Exceptionless 了！", "Notice_Submit_Exception": "您也可以通过 <code>{{sampleCode}}</code> <a href=\"{{docUrl}}\" target=\"_blank\">向 Exceptionless 发送已处理的异常</a>。", "Notice_CommandLine_Application": "请查看 <a target=\"_blank\" href=\"https://exceptionless.com/docs/api/\">Exceptionless 使用说明</a>以获取更多的信息和故障排除提示。", "Notice_DotNet_Application": "请查看 <a target=\"_blank\" href=\"https://exceptionless.com/docs/clients/dotnet/\">Exceptionless 使用说明</a>以获取更多的信息和故障排除提示。", "Notice_JavaScript_Application": "请查看 <a target=\"_blank\" href=\"https://exceptionless.com/docs/clients/javascript/\">Exceptionless 使用说明</a>以获取更多的信息以及基于特定平台的集成方法（例如 AngularJS, Express.js, React, Vue）。", "Copy not supported.": "不支持复制。", "Press ⌘-C to copy.": "按 ⌘-C 键复制。", "Press Ctrl-C to copy.": "按 Ctrl-C 键复制。", "An error occurred while getting the API key for your project.": "在获取您的项目的 API 的密钥的过程中发生了错误。", "Console and Service applications": "控制台和服务应用", "Browser applications": "浏览器应用", "Cannot_Find_Project": "无法找到项目\"{{projectId}}\"。", "An error occurred while creating the organization.": "在创建组织的过程中发生了错误。", "An error occurred while creating the project.": "在创建项目的过程中发生了错误。", "<New Organization>": "<新组织>", "Add to Slack": "新增 Slack 通知", "Project Most Frequent": "高频日志", "Please upgrade your plan to enable slack integration.": "请升级您的计划以启用 Slack 整合。", "An error occurred while adding Slack to your project.": "在将 Slack 整合到您的项目的过程中发生了错误。", "An error occurred while creating a new API key for your project.": "在为您的项目创建新的 API 密钥的过程中发生了错误。", "An error occurred while saving the configuration setting.": "在保存配置参数的过程中发生了错误。", "An error occurred loading the api keys.": "在加载 API 密钥的过程中发生了错误。", "An error occurred loading the notification settings.": "在加载通知设置的过程中发生了错误。", "An error occurred while loading the slack notification settings.": "在加载 Slack 通知设置的过程中发生了错误。", "Are you sure you want to delete this configuration setting?": "您确认要删除此配置参数吗？", "DELETE CONFIGURATION SETTING": "删除配置参数", "An error occurred while trying to delete the configuration setting.": "在尝试删除配置参数的过程中发生了错误。", "An error occurred while trying to delete the project.": "在尝试删除项目的过程中发生了错误。", "Are you sure you want to remove slack support?": "您确认要删除对 Slack 的支持吗？", "An error occurred while trying to remove slack.": "在删除 Slack 整合的过程中出现了错误。", "Are you sure you want to delete this API key?": "您确认要删除此 API 密钥吗？", "DELETE API KEY": "删除 API 密钥", "An error occurred while trying to delete the API Key.": "在尝试删除 API 密钥的过程中发生了错误。", "Are you sure you want to delete this web hook?": "您确认要删除此 Web Hook 吗？", "DELETE WEB HOOK": "删除 Web Hook", "An error occurred while trying to delete the web hook.": "在尝试删除 Web Hook 的过程中发生了错误。", "Are you sure you want to reset the data for this project?": "您确认要重置此项目的数据？", "RESET PROJECT DATA": "重置项目数据", "An error occurred while resetting project data.": "在重置项目数据的过程中发生了错误。", "An error occurred while saving the project.": "在保存项目的过程中发生了错误。", "An error occurred while saving the API key note.": "在保存 API 密钥的备注的过程中发生了错误。", "An error occurred while saving the common methods.": "在保存常用方法的过程中发生了错误。", "An error occurred while saving the data exclusion.": "在保存数据排除的过程中发生了错误。", "An error occurred while saving the include private information setting.": "保存包含私人信息设置时发生错误。", "An error occurred while saving the user agents.": "在保存 User Agent 的过程中发生了错误。", "An error occurred while saving the user namespaces.": "在保存用户命名空间的过程中发生了错误。", "An error occurred while saving your slack notification settings.": "在保存您的 Slack 通知设置的过程中发生了错误。", "Project_Deleted": "项目\"{{projectId}}\"已被删除。", "My Organizations": "我的组织", "Plan": "计划", "Projects": "项目", "View Invoices": "查看发票", "Leave Organization": "离开组织", "New Organization": "新增组织", "Monthly Usage": "月度用量", "Invite User": "邀请用户", "Delete Organization": "删除组织", "UserManager": "用户管理", "Billing": "在线支付", "Total number of projects": "项目总数", "Total number of stacks": "堆栈总数", "Total number of events within the retention period": "保留期内事件总数", "No organizations were found with the current filter.": "未找到满足当前筛选条件的组织。", "No organizations were found.": "尚未发现任何组织。", "View Payment": "查看款项", "View Stripe Invoice": "查看Stripe发票", "No invoices were found.": "尚未发现任何发票。", "Organization Name is required.": "请填写组织名称。", "A organizations with this name already exists.": "已经存在相同名称的组织。", "The usage data above is refreshed periodically and may not reflect current totals.": "上述使用量数据为定期刷新结果，可能无法准确地反映当前的真实情况。", "Click here to change your plan or billing information.": "点击这里更改您的计划或者在线支付信息。", "Change your plan or billing information.": "更改您的计划或者在线支付信息。", "Organization_Billing_Plan": "您当前在使用<strong>{{planName}}</strong>计划。", "An error occurred while loading your organizations.": "在加载您的组织的过程中发生了错误。", "Cannot_Find_Organization": "无法找到组织\"{{organizationId}}\"。", "An error occurred while inviting the user.": "在邀请用户的过程中发生了错误。", "Organization_Deleted": "组织\"{{organizationId}}\"已被删除。", "Are you sure you want to leave this organization?": "您确定要离开此组织吗？", "An error occurred while trying to leave the organization.": "在离开组织的过程中发生了错误。", "Are you sure you want to delete this organization?": "您确认要删除此组织吗？", "Successfully queued the organization for deletion.": "已将此组织放入队列等待删除。", "An error occurred while trying to delete the organization.": "在删除组织的过程中发生了错误。", "An error occurred while saving the organization.": "在保存组织的过程中发生了错误。", "Configure_Project_Title": "配置{{projectName}}（{{organizationName}}）", "Invoice #": "发票编号", "Invoice Date": "发票日期", "Invoice": "发票", "Receipt": "收据", "Paid": "已支付", "Unpaid": "未支付", "Amount": "金额", "Email:": "邮箱：", "Website:": "网站：", "Organization_Payment_Plan": "<strong>{{organization}}</strong>当前在使用<strong>{{planName}}</strong>计划。", "Payment_Select_New_Plan": "选择新计划(<a href=\"https://exceptionless.com/pricing\" target=\"_blank\">查看计划详情</a>)。<em>所有的计划变更均会按使用比例计费。</em>", "Credit Card": "信用卡", "Coupon code": "优惠码", "Exceptionless Plan": "Exceptionless 计划", "Organization to change": "变更计划的组织", "Use a new credit card": "使用新信用卡", "Card number": "卡号", "Expires": "有效期", "Name on card": "持卡人姓名", "Card code": "安全码", "Help us improve Exceptionless!": "请帮助我们改进 Exceptionless！", "Contact Us": "联系我们", "Changing Plan": "正在变更计划", "Please contact support for more information.": "请联系客户服务以获取更多的信息。", "Cannot_Find_Invoice": "无法找到发票\"{{invoiceId}}\"。", "An error occurred while changing plans.": "再变更计划的过程中发生了错误。", "Thanks! Your billing plan has been successfully changed.": "非常感谢！您的在线支付计划已经成功变更。", "An error occurred while loading available billing plans.": "在加载有效的支付计划的过程中发生了错误。", "An error occurred while loading your user account.": "在加载您的账号的过程中发生了错误。", "Credit_Card_Ending": "使用以 {{card_last4}} 结尾的信用卡", "Email": "邮箱", "Full Name": "姓名", "Email Address": "邮箱地址", "Email address (no spam)": "邮箱地址（无垃圾邮件）", "Notifications": "通知", "Password": "密码", "Current Password": "当前密码", "Current Password is required.": "请填写当前密码。", "New Password": "新密码", "New Password is required.": "请填写新密码。", "New Password must be at least 6 characters long.": "新密码不能少于6个字符。", "New Password must be less than 101 characters long.": "新密码不能多于101个字符。", "Confirm password": "确认密码", "New Password and Confirmation Password fields do not match.": "新密码与确认密码不一致。", "Confirm Password is required.": "请填写确认密码。", "Confirm Password must be at least 6 characters long.": "确认密码不能少于6个字符。", "Confirm Password must be less than 101 characters long.": "确认密码不能多于101个字符。", "Set Password": "设置密码", "Changing Password": "修改密码", "DELETE ACCOUNT": "删除帐户", "External Logins": "外部账号", "Add an external login": "新增外部账号", "Existing external logins": "现有外部账号", "Remove": "移除", "No external logins were found.": "尚未发现外部账号。", "Log in using your Microsoft account": "使用您的微软账号登录", "Log in using your Google account": "使用您的谷歌账号登录", "Log in using your Facebook account": "使用您的Facebook账号登录", "Log in using your GitHub account": "使用您的Github账号登录", "An error occurred while adding external login.": "在增加外部账号的过程中发生了错误。", "An error occurred while loading the notification settings.": "在加载通知设置的过程中发生了错误。", "An error occurred while loading the projects.": "在加载项目的过程中发生了错误。", "An error occurred while loading your user profile.": "在加载您的个人资料的过程中发生了错误。", "Are you sure you want to delete your account?": "您确认要删除您的账号？", "Successfully removed your user account.": "已经成功移除了您的账号。", "An error occurred while trying remove your user account.": "在移除您的账号的过程中发生了错误。", "An error occurred while sending your verification email.": "在给您发送验证邮件的过程中发生了错误。", "An error occurred while saving your email address.": "在保存您的邮箱地址的过程中发生了错误。", "An error occurred while saving your notification settings.": "在保存您的通知设置的过程中发生了错误。", "An error occurred while saving your email notification preferences.": "在保存您的邮件通知偏好的过程中发生了错误。", "An error occurred while saving your full name.": "在保存您的姓名的过程中发生了错误。", "An error occurred while removing the external login.": "在移除您的外部账号的过程中发生了错误。", "Choose how often you want to receive notifications for event occurrences in this project.": "请选择您希望以什么样的频率接收此项目的通知。", "Are you sure you want to add the admin role for this user?": "您确定要为此用户增加管理员角色？", "Are you sure you want to remove the admin role from this user?": "您确认要移除此用户的管理员角色？", "An error occurred while add the admin role.": "在增加管理员角色的过程中出现了错误。", "An error occurred while remove the admin role.": "在移除管理员角色的过程中出现了错误。", "Log in": "登录", "after": "后", "before": "以前", "with": "与", "OR": "或者", "Login with": "登录", "Signup": "注册", "Forgot password?": "忘记密码？", "Create an account": "创建帐户", "Create My Account": "创建我的帐户", "Log In": "登录", "Change password": "修改密码", "Reset your password": "重置密码", "Forgot Password": "忘记密码", "NOTICE_SIGNINGUP": "阅读并接受<a href=\"https://exceptionless.com/privacy\" target=\"_blank\">隐私保护声明</a> 及 <a href=\"https://exceptionless.com/terms\" target=\"_blank\">服务条款</a>。", "Loggin_Failed_Message": "登录过程中出现错误。如需了解更多信息，请联系客户服务人员。", "Unable_to_connect_to": "无法连接", "ResetPassword_Failed_Message": "重置密码的过程中出现错误。", "ResetPassword_Success_Message": "包含修改密码说明的邮件已经发送到您的邮箱。", "An error occurred while signing up.  Please contact support for more information.": "在注册的过程中发生了错误。如需了解更多信息，请联系客户服务人员。", "You have successfully changed your password.": "已经成功地修改了您的密码。", "An error occurred while trying to change your password.": "在修改您的密码的过程中发生了错误。", "Successfully verified your account.": "您的账号已成功验证。", "An error occurred while verifying your account.": "在验证您的账号的过程中发生了错误。", "AdminRole": "管理员", "Invited": "已邀请", "Resend": "重新发送", "Your first and last name": "您的姓名", "verification email.": "验证邮件。", "Email not verified.": "邮箱地址尚未验证。", "Email notifications are currently disabled.": "邮件通知已被禁用。", "To enable email notifications you must first verify your email address.": "如需启用邮件通知，首先必须验证您的邮箱地址。", "Enable email notifications": "启用邮件通知", "Send daily project summary": "发送每日项目简报", "Notify me on new errors": "通知我新的错误", "Notify me on critical errors": "通知我严重错误", "Notify me on error regressions": "通知我回归错误", "Notify me on new events": "通知我新的事件", "Notify me on critical events": "通知我关键事件", "Upgrade now": "现在升级", "to enable occurrence level notifications!": "以启用实时邮件通知！", "Resend Invite Email": "重新发送邀请邮件", "Revoke Invite": "撤销邀请", "Remove User": "移除用户", "Add Admin Role": "添加管理员角色", "Remove Admin Role": "移除管理员角色", "No users were found.": "找不到用户。", "Are you sure you want to remove this user from your organization?": "您确认要从您的组织中删除此用户吗？", "An error occurred while trying to remove the user.": "在尝试删除此用户的过程中发生了错误。", "An error occurred while trying to resend the notification.": "在尝试重发通知的过程中发生了错误。", "Create New Web Hook": "新增Web Hook", "Url": "链接地址", "Enter the URL to call": "输入被调用的链接地址", "URL is required.": "请输入链接地址。", "Control when the web hook is called by choosing the event types below.": "选择下面的事件类型以控制何时对 Web Hook 进行调用。", "Please choose one or more event types.": "请至少选择一个事件类型。", "Create Web Hook": "创建 Web Hook", "New Error": "新的错误", "Occurs when a new error that has never been seen before is reported to your project.": "当您的项目中发生尚未出现过得错误的情况下触发。", "Critical Error": "关键错误", "Occurs when an error that has been marked as critical is reported to your project.": "当您的项目中的错误被标记为关键事件的情况下触发。", "Regression": "回归事件", "Occurs when an event that has been marked as fixed has reoccurred in your project.": "当您的项目中出现被标记为\"已修复\"的事件的情况下触发。", "Occurs when a new event that has never been seen before is reported to your project.": "当您的项目中出现尚未被记录过的事件的情况下触发。", "Critical Event": "关键事件", "Occurs when an event that has been marked as critical is reported to your project.": "当您的项目中的事件被标记为关键事件的情况下触发。", "Promoted": "外部推送", "Used to promote event stacks to external systems.": "用于将堆栈推送到外部系统。", "Delete": "删除", "Cancel": "取消", "Apply": "确认", "DateTime": "{{date | date: 'yyyy年M月d日 H:mm:ss'}}", "Go To Most Frequent": "前往最常去的地方", "Save": "保存", "Edit": "修改", "to": "至", "Email Address is required.": "请填写邮箱地址", "Password is required": "请填写密码", "Password must be at least 6 characters long": "请填写长度不小于6位的密码", "Password must be less than 101 characters long": "请填写长度不大于101位的密码", "Signup for a FREE account in seconds": "在几秒钟内注册一个免费帐户", "Full Name is required.": "请填写姓名", "A user already exists with this email address.": "邮箱已存在", "Already have an account?": "已经有账户?", "API rate limit exceeded": "API 调用频率超限", "Your API rate limit has been exceeded. Please try again in 15 minutes.": "您的应用程序调用 API 的频率超限。请在15分钟之后重试。", "to enable premium features and extra storage!": "以启用高级特性及更多的存储空间！", "to enable search and other premium features!": "以启用搜索及其他高级特性！", "is currently on a free plan.": "当前在使用免费计划。", "has reached its monthly plan limit.": "已经达到了月度使用计划上限。", "to continue receiving events.": "以继续接收日志。", "to increase your limits.": "以提升您的上限。", "Events are currently being throttled to prevent using up your plan limit in a small window of time.": "当前系统会进行日志量的节流控制，以避免在短时间内超出您的使用计划上限。", "Events are currently being throttled for": "当前系统为这些组织进行了日志量的节流控制：", "API requests are currently being throttled for": "当前系统为这些组织进行了 API 请求的节流控制：", "Please note that while your account is suspended all new client events will be discarded.": "请注意您的账户已经被冻结，所有客户端发送的日志都会被丢弃。", "We haven't received any data!": "我们还没接收到任何数据！", "Setup your first project": "设置您的第一个项目", "Copied!": "复制好了！", "Message:": "错误消息：", "Allowed": "容许量", "Blocked": "阻滞量", "Too Big": "超型量", "Limit": "上限量", "Allowed in Organization": "组织容许量", "Occurrences": "数量", "Log source": "日志来源", "Add": "新增", "Total": "总计", "Log Level": "日志级别", "trace": "跟踪", "debug": "除错", "info": "信息", "warn": "警告", "error": "错误", "fatal": "致命", "off": "关", "Default": "默认", "Notice_Default_Log_Level": "默认日志级别控制日志事件应接受的最低日志级别。 日志级别也可以在日志堆栈级别覆盖。", "DIALOGS_ERROR": "错误", "DIALOGS_ERROR_MSG": "出现未知错误。", "DIALOGS_CLOSE": "关闭", "DIALOGS_PLEASE_WAIT": "请稍候", "DIALOGS_PLEASE_WAIT_ELIPS": "请稍候...", "DIALOGS_PLEASE_WAIT_MSG": "请等待操作完成。", "DIALOGS_PERCENT_COMPLETE": "% 已完成", "DIALOGS_NOTIFICATION": "通知", "DIALOGS_NOTIFICATION_MSG": "未知应用程序的通知。", "DIALOGS_CONFIRMATION": "确认", "DIALOGS_CONFIRMATION_MSG": "确认要求。", "DIALOGS_JSON": "JSON", "DIALOGS_OK": "确定", "DIALOGS_YES": "确认", "DIALOGS_NO": "取消", "NOTICE_GRAVATAR": "您的头像是根据下面所填写的邮箱地址调用 <a href=\"https://gravatar.com\" target=\"_blank\">Gravatar</a> 的服务生成。"}