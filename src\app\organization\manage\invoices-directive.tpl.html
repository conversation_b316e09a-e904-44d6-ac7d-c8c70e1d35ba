<div class="table-responsive">
  <table class="table table-striped table-bordered table-fixed b-t" refresh-on="OrganizationChanged PlanChanged" refresh-action="vm.get(vm.currentOptions, false)" refresh-throttle="10000">
    <thead>
    <tr>
      <th>{{::'Payment Number' | translate}}</th>
      <th>{{::'Date' | translate}}</th>
      <th class="number">{{::'Status' | translate}}</th>
      <th class="action">{{::'Actions' | translate}}</th>
    </tr>
    </thead>
    <tbody>
    <tr ng-repeat="invoice in vm.invoices track by invoice.id">
      <td class="row-clickable" ng-click="vm.open(invoice.id)">{{invoice.id}}</td>
      <td class="row-clickable" ng-click="vm.open(invoice.id)">{{'DateTime' | translate : invoice }}</td>
      <td class="row-clickable" ng-click="vm.open(invoice.id)">{{invoice.paid ? 'Paid' : 'Unpaid'}}</td>
      <td>
        <div class="btn-group">
          <button type="button" role="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-fw fa-edit"></i> <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right" role="menu">
            <li><a ng-click="vm.open(invoice.id)"><i class="fa fa-file"></i> {{::'View Payment' | translate}}</a></li>
            <li ng-if="vm.hasAdminRole(appVm.user)">
              <a ng-href="https://manage.stripe.com/invoices/in_{{::invoice.id}}" target="_blank"><i class="fa fa-credit-card"></i> {{::'View Stripe Invoice' | translate}}</a>
            </li>
          </ul>
        </div>
      </td>
    </tr>
    <tr ng-if="vm.invoices.length === 0">
      <td colspan="4">
        <strong>{{::'No invoices were found.' | translate}}</strong>
      </td>
    </tr>
    </tbody>
  </table>

  <div class="table-footer" ng-if="vm.previous || vm.next">
    <div class="row">
      <div class="col-sm-8 col-xs-8 text-center" ng-if="vm.pageSummary">
        <small class="text-muted inline m-t-xs">{{vm.pageSummary}}</small>
      </div>
      <div class="col-sm-4 col-xs-4 text-right">
        <ul class="pagination pagination-sm m-t-none m-b-none">
          <li ng-show="vm.currentOptions.page && vm.currentOptions.page > 2"><a ng-click="vm.get()"><i class="fa fa-fast-backward"></i></a></li>
          <li ng-class="{'disabled': !vm.previous}"><a ng-disabled="!vm.previous" ng-click="!vm.previous || vm.previousPage()"><i class="fa fa-chevron-left"></i></a></li>
          <li ng-class="{'disabled': !vm.next}"><a ng-disabled="!vm.next" ng-click="!vm.next || vm.nextPage()"><i class="fa fa-chevron-right"></i></a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
