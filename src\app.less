@import (inline) "bower_components/angular-dialog-service/dist/dialogs.css";
@import (inline) "bower_components/angular-hotkeys/src/hotkeys.css";
@import (inline) "bower_components/angular-loading-bar/src/loading-bar.css";
@import (inline) "bower_components/angular-xeditable/dist/css/xeditable.css";
@import (inline) "bower_components/angularjs-toaster/toaster.css";
@import (inline) "bower_components/animate.css/animate.css";
@import (inline) "bower_components/bootstrap-daterangepicker/daterangepicker.css";
@import (inline) "bower_components/rickshaw/rickshaw.css";

/* Required for Angular UI Bootstrap */
.nav, .pagination, .carousel, .panel-title a { cursor: pointer; }

/* app */
@import "less/variables.less";
@import "less/mixins.less";
@import "less/reset.less";
@import "less/alerts.less";
@import "less/forms.less";
@import "less/labels.less";
@import "less/layout.less";
@import "less/list-group.less";
@import "less/nav.less";
@import "less/buttons.less";
@import "less/components.less";
@import "less/tables.less";
@import "less/ng.less";
@import "less/colors.less";
@import "less/utilities.less";

/* Component LESS */
@import "app/auth/auth.less";
@import "app/event/event.less";
@import "app/payment/payment.less";
@import "app/project/project.less";
@import "app/reports/reports.less";
@import "app/session/session.less";
@import "components/date-range-picker/date-range-picker-directive.less";
@import "components/dialog/confirm-dialog.less";
@import "components/dialog/json-dialog.less";
@import "components/events/events-directive.less";
@import "components/loading-bar/loading-bar-directive.less";
@import "components/log-level/log-level-directive.less";
@import "components/object-dump/object-dump-directive.less";
@import "components/project-filter/project-filter-directive.less";
@import "components/rickshaw/rickshaw-directive.less";
@import "components/search-filter/search-filter.less";
@import "components/simple-stack-trace/simple-stack-trace-directive.less";
@import "components/stack-trace/stack-trace-directive.less";
@import "components/summary/summary-directive.less";
