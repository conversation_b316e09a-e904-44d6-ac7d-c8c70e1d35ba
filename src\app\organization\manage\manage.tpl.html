<organization-notifications organization-id="vm.organization.id"></organization-notifications>

<div class="hbox hbox-auto-xs hbox-auto-sm" refresh-on="OrganizationChanged" refresh-action="vm.get(data)" refresh-debounce="5000">
  <div class="col">
    <div class="wrapper-md">
      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-th-list"></i> {{::'Manage Organization' | translate}} {{ vm.organization.name ? ' "' + vm.organization.name + '"' : ''}}</div>
        <div class="panel-body m-b-n">
          <uib-tabset class="tab-container" active="vm.activeTabIndex">
            <uib-tab heading="{{::'General' | translate}}">
              <form name="vm.organizationForm" role="form" class="form-validation">
                <div class="form-group m-b-none">
                  <label for="name">{{::'Organization Name' | translate}}</label>
                  <div ng-class="{'input-group': !!vm.organizationForm.$pending }">
                    <input id="name" name="name" type="text" class="form-control" placeholder="{{::'Organization Name' | translate}}"
                           ng-model="vm.organization.name"
                           ng-model-options="{ debounce: 500 }"
                           ng-change="vm.save(vm.organizationForm.$valid)"
                           organization-name-available-validator
                           ng-required="true"
                           autofocus />
                    <span class="input-group-addon" ng-if="vm.organizationForm.$pending">
                      <i class="fa fa-fw fa-spinner fa-spin"></i>
                    </span>
                  </div>

                  <div class="error" ng-messages="vm.organizationForm.name.$error" ng-if="vm.organizationForm.$submitted || vm.organizationForm.name.$touched">
                    <small ng-message="required">{{::'Organization Name is required.' | translate}}</small>
                    <small ng-message="unique">{{::'A organizations with this name already exists.' | translate}}</small>
                  </div>
                </div>
              </form>

              <div ng-show="vm.hasMonthlyUsage">
                <h4 style="margin-top: 20px;">{{::'Monthly Usage' | translate}}</h4>
                <p>
                  You are currently on the <a ng-if="vm.canChangePlan" ng-click="vm.changePlan()"><strong>{{vm.organization.plan_name}}</strong> plan</a> with
                  <b ng-class="{'text-warning': vm.remainingEventLimit === 0}">{{vm.remainingEventLimit | number}}</b> events remaining until this billing period's limit is reset on <b>{{vm.next_billing_date | date: 'longDate'}}</b> (<timeago date="vm.next_billing_date"></timeago>).
                  <a ng-if="vm.canChangePlan" ng-click="vm.changePlan()">{{::'Click here to change your plan or billing information.' | translate}}</a>
                </p>
                <rickshaw options="vm.chart.options" features="vm.chart.features"></rickshaw>
                <br class="clearfix" />
                <h6><em>{{::'The usage data above is refreshed periodically and may not reflect current totals.' | translate}}</em></h6>
              </div>
            </uib-tab>
            <uib-tab heading="{{::'Projects' | translate}}">
              <projects settings="vm.projects"></projects>

              <a ui-sref="app.project.add({ organizationId: vm.organization.id })" class="btn btn-primary" role="button">{{::'Add New Project' | translate}}</a>
            </uib-tab>
            <uib-tab heading="{{::'UserManager' | translate}}">
              <users settings="vm.users"></users>

              <button type="button" role="button" ng-click="vm.addUser()" class="btn btn-primary">{{::'Invite User' | translate}}</button>
            </uib-tab>
            <uib-tab heading="{{::'Billing' | translate}}">
              <p><span ng-bind-html="'Organization_Billing_Plan' | translate:{planName: vm.organization.plan_name}"></span> <a ng-if="vm.canChangePlan" ng-click="vm.changePlan()">{{::'Change your plan or billing information.' | translate}}</a></p>
              <invoices settings="vm.invoices"></invoices>
            </uib-tab>
          </uib-tabset>
        </div>
        <footer class="panel-footer">
          <div class="pull-right">
            <a ui-sref="app.organization-frequent({ organizationId: vm.organization.id })" class="btn btn-default" role="button">{{::'Go To Most Frequent' | translate}}</a>
          </div>
          <div class="btn-group">
            <button type="button" role="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-fw fa-remove"></i> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
              <li><a ng-click="vm.leaveOrganization(appVm.user)" role="button">{{::'Leave Organization' | translate}}</a></li>
              <li><a ng-click="vm.removeOrganization()" role="button">{{::'Delete Organization' | translate}}</a></li>
            </ul>
          </div>
        </footer>
      </div>
    </div>
  </div>
</div>
