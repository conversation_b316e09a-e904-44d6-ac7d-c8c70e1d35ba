<div class="hbox hbox-auto-xs hbox-auto-sm">
  <div class="col" refresh-on="OrganizationChanged" refresh-action="vm.getOrganizations()" refresh-throttle="10000">
    <div class="wrapper-md">
      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-asterisk"></i> {{::'Project Information' | translate}}</div>
        <div class="panel-body">
          <form name="vm.addForm" role="form" class="form-validation">
            <div class="form-group" ng-if="vm.hasOrganizations()">
              <label for="organization">{{::'Organization Name' | translate}}</label>
              <select id="organization" class="form-control"
                      ng-model="vm.currentOrganization"
                      ng-options="organization.name for organization in vm.organizations track by organization.id"></select>
            </div>

            <div class="form-group" ng-if="vm.canCreateOrganization()">
              <div ng-class="{'input-group': !!vm.addForm.$pending }">
                <input name="organization_name" type="text" class="form-control" placeholder="{{::'New Organization Name' | translate}}"
                       ng-model="vm.organization_name"
                       ng-model-options="{ debounce: 500 }"
                       organization-name-available-validator
                       ng-required="vm.canCreateOrganization()"
                       autofocus />

                <span class="input-group-addon" ng-if="vm.addForm.$pending">
                  <i class="fa fa-fw fa-spinner fa-spin"></i>
                </span>
              </div>

              <div class="error" ng-messages="vm.addForm.organization_name.$error" ng-if="vm.addForm.$submitted || vm.addForm.organization_name.$touched">
                <small ng-message="required">{{::'Organization Name is required.' | translate}}</small>
                <small ng-message="unique">{{::'A organizations with this name already exists.' | translate}}</small>
              </div>
            </div>

            <div class="form-group">
              <label for="project_name">{{::'Project Name' | translate}}</label>
              <div ng-class="{'input-group': !!vm.addForm.$pending }">
                <input id="project_name" name="project_name" type="text" class="form-control" placeholder="{{::'New Project Name' | translate}}"
                       ng-model="vm.project_name"
                       ng-model-options="{ debounce: 500 }"
                       project-name-available-validator
                       organization-id="vm.currentOrganization.id"
                       ng-required="true"
                       autofocus />

                  <span class="input-group-addon" ng-if="vm.addForm.$pending">
                    <i class="fa fa-fw fa-spinner fa-spin"></i>
                  </span>
              </div>

              <div class="error" ng-messages="vm.addForm.project_name.$error" ng-if="vm.addForm.$submitted || vm.addForm.project_name.$touched">
                <small ng-message="required">{{::'Project Name is required.' | translate}}</small>
                <small ng-message="unique">{{::'A project with this name already exists.' | translate}}</small>
              </div>
            </div>
          </form>
        </div>
        <footer class="panel-footer">
          <div class="pull-right">
            <a ui-sref="app.frequent" ng-if="vm.hasOrganizations()" class="btn btn-default" role="button">{{::'Go To Most Frequent' | translate}}</a>
          </div>
          <div class="clearfix">
            <input ng-click="vm.add()" type="submit" role="button" class="btn btn-primary" value="{{::'Add Project' | translate}}" />
          </div>
        </footer>
      </div>
    </div>
  </div>
</div>
