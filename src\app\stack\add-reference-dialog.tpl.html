<form name="addReferenceForm" role="form" class="form-validation" autocomplete="on">
  <div class="modal-header dialog-header-confirm">
    <button type="button" role="button" class="close" ng-click="vm.cancel()">&times;</button>
    <h4 class="modal-title"><i class="fa fa-external-link fa-fw"></i> {{::'Please enter a Reference Link' | translate}}</h4>
  </div>

  <div class="modal-body">
    <div class="form-group">
      <label for="url">{{::'Reference Link' | translate}}</label>
      <input id="url" name="url" type="url" class="form-control input-lg" placeholder="{{::'Please enter a valid URL' | translate}}"
             ng-model="vm.data.url"
             ng-required="true"
             autofocus />

      <div class="error" ng-messages="addReferenceForm.url.$error" ng-if="addReferenceForm.$submitted || addReferenceForm.url.$touched">
        <small ng-message="required">{{::'Reference Link is required.' | translate}}</small>
        <small ng-message="url">{{::'Reference Link is required.' | translate}}</small>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" role="button" class="btn btn-default" ng-click="vm.cancel()">{{::'Cancel' | translate}}</button>
    <input type="submit" role="button" class="btn btn-primary" ng-click="vm.save(addReferenceForm.$valid)" value="{{::'Save Reference Link' | translate}}" />
  </div>
</form>
