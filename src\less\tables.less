.row-clickable {
    cursor: pointer;
}

.table-fixed {
    table-layout: fixed;
    word-wrap: break-word;
}

.table th.clickable {
  cursor: pointer;
  cursor: hand;
}

.table th.action {
    width: 78px;
}

.table th.action-lg {
  width: 136px;
}

.table th.action-xl {
  width: 175px;
}

.table th.action-xxl {
  width: 210px;
}

.table th.date {
    white-space: nowrap;
    width: 140px;
}

.table th.relative-date {
  white-space: nowrap;
  width: 155px;
}

.table th.number {
    white-space: nowrap;
    width: 88px;
}

.table th.percentage {
  white-space: nowrap;
  width: 74px;
}

td.number {
  text-align: right;
}

.table th.selection {
    width: 50px;
}

.table-key-value td+td, .table-key-value th+td {
    width: 100%
}

.table-key-value th:first-child, .table-key-value td:first-child {
    @media (max-width: @screen-xs-max) {
        width: 120px;
    }

    @media (min-width: @screen-sm-min) and (max-width: @screen-sm-max) {
        width: 120px;
    }

    @media (min-width: @screen-md-min) and (max-width: @screen-md-max) {
        width: 140px;
    }

    @media (min-width: @screen-lg-min) {
        width: 140px;
    }
}

.table-selectable th:nth-child(2), .table-selectable td:nth-child(2) {
  @media (max-width: @screen-xs-max) {
    border-left: none;
  }
}

.table-key-value td>ul {
  padding-left: 15px;
}

.table-footer {
    padding: 10px 15px;
    background-color: #fff;
    border-top: 1px solid #ddd;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
}

.tab-pane .table-footer {
  padding: inherit;
  border-top: inherit;
}

.tab-content .table {
  @media (min-width: @screen-sm-min) and (max-width: @screen-sm-max) {
    margin-bottom: 15px;
  }

  @media (min-width: @screen-md-min) and (max-width: @screen-md-max) {
    margin-bottom: 15px;
  }

  @media (min-width: @screen-lg-min) {
    margin-bottom: 15px;
  }
}

// Override default styles. This fixes dropdowns not being cut off and long content not running over other td's.
.table-responsive {
  overflow-x: inherit;

  @media screen and (max-width: @screen-xs-max) {
    overflow-y: inherit;

    > .table {
      > thead,
      > tbody,
      > tfoot {
        > tr {
          > th,
          > td {
            white-space: inherit;
          }
        }
      }
    }
  }
}

.editable-wrap {
  width: 100%;
}
