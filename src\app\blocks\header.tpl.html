<div class="bg-black primary-underline">
  <div class="navbar-header">
    <button type="button" role="button" class="pull-right visible-xs dk" ui-toggle-class="show" data-toggle="collapse" data-target=".navbar-collapse">
      <i class="fa fa-fw fa-cog"></i>
    </button>
    <button type="button" role="button" class="pull-right visible-xs" ui-toggle-class="off-screen" data-target=".app-aside" ui-scroll="app">
      <i class="fa fa-fw fa-align-justify"></i>
    </button>
    <a ng-href="{{appVm.urls.frequent.all}}" class="navbar-brand text-lt">
      <img src="/touch-icon-ipad-114.png" alt="exceptionless" class="icon">
      <img src="/img/exceptionless-logo.png" alt="exceptionless" class="hidden-folded">
    </a>
  </div>

  <div class="collapse navbar-collapse no-shadow">
    <div class="nav navbar-nav navbar-left hidden-xs">
      <a class="btn no-shadow navbar-btn" role="button" ng-click="appVm.toggleSideNavCollapsed()">
        <i class="fa {{appVm.isSideNavCollapsed ? 'fa-indent' : 'fa-dedent'}} fa-fw"></i>
      </a>
    </div>

    <ul class="nav navbar-nav navbar-left">
      <project-filter></project-filter>
      <date-filter></date-filter>
    </ul>

    <ul class="nav navbar-nav navbar-right hidden-xs" auto-active>
      <li class="dropdown">
        <a class="dropdown-toggle profile-image" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
          <img class="img-thumbnail" gravatar-src="appVm.user.email_address" gravatar-size="35" alt="{{appVm.user.full_name}}" />
          <span class="caret"></span>
        </a>
        <ul class="dropdown-menu">
          <li><a ui-sref="app.account.manage"><i class="fa fa-user fa-fw"></i> {{::'My Account' | translate}}</a></li>
          <li><a ng-click="appVm.changePlan()" ng-if="appVm.canChangePlan"><i class="fa fa-credit-card fa-fw"></i> {{::'Change Plan' | translate}}</a></li>
          <li><a ui-sref="app.account.manage({ tab: 'password' })"><i class="fa fa-lock fa-fw"></i> {{::'Change Password' | translate}}</a></li>
          <li><a href="https://exceptionless.com/docs/" target="_blank"><i class="fa fa-book fa-fw"></i> {{::'Documentation' | translate}}</a></li>
          <li ng-if="appVm.isIntercomEnabled()"><a ng-click="appVm.showIntercom()"><i class="fa fa-comment fa-fw"></i> {{::'Support' | translate}}</a></li>
          <li><a ui-sref="auth.logout"><i class="fa fa-power-off fa-fw"></i> {{::'Logout' | translate}}</a></li>
        </ul>
      </li>
    </ul>

    <search-filter></search-filter>

    <ul class="nav navbar-nav visible-xs hidden-sm" auto-active>
      <li><a ui-sref="app.account.manage"><i class="fa fa-user fa-fw"></i> {{::'My Account' | translate}}</a></li>
      <li><a ng-click="appVm.changePlan()" ng-if="appVm.canChangePlan"><i class="fa fa-credit-card fa-fw"></i> {{::'Change Plan' | translate}}</a></li>
      <li><a ui-sref="app.account.manage({ tab: 'password' })"><i class="fa fa-lock fa-fw"></i> {{::'Change Password' | translate}}</a></li>
      <li><a href="https://exceptionless.com/docs/" target="_blank"><i class="fa fa-book fa-fw"></i> {{::'Documentation' | translate}}</a></li>
      <li ng-if="appVm.isIntercomEnabled()"><a ng-click="appVm.showIntercom()"><i class="fa fa-comment fa-fw"></i> {{::'Support' | translate}}</a></li>
      <li><a ui-sref="auth.logout"><i class="fa fa-power-off fa-fw"></i> {{::'Logout' | translate}}</a></li>
    </ul>
  </div>
</div>
