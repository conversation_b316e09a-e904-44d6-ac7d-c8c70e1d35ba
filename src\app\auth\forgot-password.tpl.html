<div>
  <rate-limit></rate-limit>

  <div class="container w-auto-xs">
    <div class="text-center m-t">
      <a href="https://exceptionless.com">
        <img src="/img/exceptionless-350.png" alt="logo" />
      </a>
    </div>
    <div class="hbox hbox-auto-xs hbox-auto-sm">
      <div class="col">
        <div class="wrapper-md">
          <div class="panel panel-default">
            <div class="panel-heading"><strong>{{::'Reset your password' | translate}}</strong></div>
            <div class="panel-body">
              <form name="forgotForm" role="form" class="form-validation form-horizontal" autocomplete="on">
                <div class="form-group">
                  <label for="email" class="col-sm-2 control-label">{{::'Email' | translate}}</label>
                  <div class="col-sm-10">
                    <input id="email" name="email" type="email" class="form-control" placeholder="{{::'Email Address' | translate}}" x-autocompletetype="email" autocorrect="off" spellcheck="false" ng-model="vm.email" ng-required="true" autofocus />
                    <div class="error" ng-messages="forgotForm.email.$error" ng-if="forgotForm.$submitted || forgotForm.email.$touched">
                      <small ng-message="required">{{::'Email Address is required.' | translate}}</small>
                      <small ng-message="email">{{::'Email Address is required.' | translate}}</small>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-10">
                    <input type="submit" role="button" class="btn btn-primary" ng-click="vm.resetPassword(forgotForm.$valid)" value="{{::'Forgot Password' | translate}}" />
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
