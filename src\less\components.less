.switch{
	cursor: pointer;
	position: relative;
  display: inline-block;
  width: @switch-width;
  height: @switch-height;
  border-radius: 30px;
  background-color: @brand-success;
  margin: 0;
	input{
		position: absolute;
		.opacity(0);
		&:checked{
			+ i{
        &:before{
          top: 50%;
          bottom: 50%;
          left: 50%;
          right: 5px;
          border-width: 0;
          border-radius: 5px;
        }
				&:after{
					margin-left: @switch-width - @switch-height + 1;
				}
			}
		}
	}
	i{
    &:before{
      content: "";
      position: absolute;
      top: -1px;
      bottom: -1px;
      left: -1px;
      right: -1px;
      background-color: #fff;
      border: 1px solid #f0f0f0;
      border-radius: 30px;
      .transition(all 0.2s);
    }
		&:after{
			content: "";
			position: absolute;
			background-color: #fff;
			width: @switch-height - 2;
			top: 1px;
			bottom: 1px;
			border-radius: 50%;
			.box-shadow(1px 1px 3px rgba(0, 0, 0, 0.25));
      .transition(margin-left 0.3s);
		}
	}
}

.switch-md{
  width: @switch-md-width;
  height: @switch-md-height;
  input{
    &:checked{
      + i{
        &:after{
          margin-left: @switch-md-width - @switch-md-height + 1;
        }
      }
    }
  }
  i{
    &:after{
      width: @switch-md-height - 2;
    }
  }
}

.switch-lg{
  width: @switch-lg-width;
  height: @switch-lg-height;
  input{
    &:checked{
      + i{
        &:after{
          margin-left: @switch-lg-width - @switch-lg-height + 1;
        }
      }
    }
  }
  i{
    &:after{
      width: @switch-lg-height - 2;
    }
  }
}

.switch-text {
  float: right !important;
  margin-top: -1px;
  padding-left: 7px;
  cursor: pointer;
}

.checks{
  padding-left: 20px;
  cursor: pointer;
  input{
    opacity: 0;
    position: absolute;
    margin-left: -20px;
    &:checked + i{
      border-color: @brand-info;
      &:before{
        left: 4px;
        top: 4px;
        width: 10px;
        height: 10px;
        background-color: @brand-info;
      }
    }
    &:checked + span .active{
      display: inherit;
    }
    &[type="radio"] + i{
      &,
      &:before{
        border-radius: 50%;
      }
    }
    &[type="checkbox"]:checked + i:before{

    }
    &[type="radio"]:checked + i:before{

    }
    &[disabled],
    fieldset[disabled] & {
      & + i{
        border-color: lighten(@input-border, 5%);
        &:before{
          background-color: lighten(@input-border, 5%);
        }
      }
    }
  }
  > i{
    width: 20px;
    height: 20px;
    line-height: 1;
    border: 1px solid @input-border;
    background-color: #fff;
    margin-left: -20px;
    margin-top: -2px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    position: relative;
    &:before{
      content:"";
      position: absolute;
      left: 10px;
      top: 10px;
      width: 0px;
      height: 0px;
      background-color: transparent;
      .transition(all 0.2s);
    }
  }
  > span{
    margin-left: -20px;
    .active{
      display: none;
    }
  }
}

// ui.bootstrap datepicker
.datepicker{margin: 0 5px}
.datepicker .btn-default{border-width: 0;box-shadow: none;}
.datepicker .btn[disabled]{opacity: 0.4}
.datepicker .btn-info .text-info{color: #fff !important;}

.pagination>li>a, .pagination>li>span {
  color: @brand-success;
}

.pagination>.disabled>span,
.pagination>.disabled>span:hover,
.pagination>.disabled>span:focus,
.pagination>.disabled>a,
.pagination>.disabled>a:hover,
.pagination>.disabled>a:focus {
  color: #D0D0D0;
}

.pagination>li>a:hover, .pagination>li>span:hover, .pagination>li>a:focus, .pagination>li>span:focus {
  color: @brand-success;
}

a.dashboard-block:hover, a.dashboard-block:active, a.dashboard-block:focus {
  color: rgba(255, 255, 255, 0.8);
}

.dashboard-block {
  background-color: #6EBC1A;
  position: relative;
  display: block;
  padding: 20px 20px;
  opacity:0.9;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom:15px;
  border: 1px solid @border-color;
  color: #fff;
  .rotate {
    z-index: 8;
    float: right;
    height: 100%;
    i {
      color: rgba(20, 20, 20, 0.2);
      position: absolute;
      left: 0;
      left: auto;
      right: 10px;
      bottom: 10px;
      display: block;
    }
  }
  .more {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.25) !important;
    text-shadow: none;
  }
  .details {
    position: relative;
    z-index: 10;
    float: left;
    margin-top: -10px;
    text-align: left;
  }
  .title {
    display: block;
    margin-bottom: 1em;
    font-size: 13px;
    text-transform: uppercase;
  }
  .sub {
    display: block;
    font-size: 32px;
  }
  .subAlternative {
    display: block;
    color: rgba(20, 20, 20, 0.8);
    font-size: 22px;
    padding-top: 10px;

    abbr[title] {
      border-bottom: 1px dotted rgba(20, 20, 20, 0.8);
    }
  }
  abbr[title] {
    border-bottom: 1px dotted #fff;
  }
}

.scrollable-menu {
    height: auto;
    overflow-x: hidden;
}
