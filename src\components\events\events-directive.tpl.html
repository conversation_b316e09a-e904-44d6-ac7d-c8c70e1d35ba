<div class="table-responsive" refresh-on="filterChanged" refresh-action="vm.get()">
  <table class="table table-striped table-bordered table-selectable table-fixed b-t table-hover table-clickable" refresh-on="StackChanged PlanChanged" refresh-if="vm.canRefresh(data)" refresh-action="vm.get(vm.currentOptions)" refresh-throttle="10000">
    <thead refresh-on="PersistentEventChanged" refresh-if="vm.canRefresh(data)" refresh-action="vm.get(vm.currentOptions)" refresh-throttle="10000">
    <tr>
      <th class="selection hidden-xs" ng-if="vm.actions.length > 0">
        <label class="checks m-b-none">
          <input type="checkbox" ng-click="vm.updateSelection()" ng-checked="vm.selectedIds.length > 0" ng-disabled="vm.events.length === 0"><i></i>
        </label>
      </th>
      <th>{{::'Summary' | translate}}</th>
      <th class="action-xl hidden-xs" ng-if="vm.showIPAddress">{{::'IP'}}</th>
      <th class="action-xl hidden-xs">{{::'User' | translate}}</th>
      <th class="clickable" ng-class="{ 'relative-date': vm.relativeTo(), 'date': !vm.relativeTo(), 'dropup': !vm.sortByDateDescending }" ng-click="vm.toggleDateSort()">
        <abbr title="{{::vm.sortByDateDescending ? 'Sort Descending' : 'Sort Ascending' | translate}}">
          {{::vm.timeHeaderText | translate}} <span class="caret"></span>
        </abbr>
      </th>
    </tr>
    </thead>
    <tbody>
      <tr ng-repeat="event in vm.events track by event.id" ng-if="vm.events.length > 0" ng-class="{ 'row-clickable': vm.currentEventId !== event.id, 'row-selected': vm.currentEventId === event.id }">
        <td class="hidden-xs" ng-if="vm.actions.length > 0"><label class="checks m-b-none"><input type="checkbox" checklist-model="vm.selectedIds" checklist-value="event.id"><i></i></label></td>
        <td ng-click="vm.open(event.id, $event)">
          <summary source="event" show-type="vm.showType"></summary>
        </td>
        <td ng-click="vm.open(event.id, $event)" ng-if="vm.showIPAddress">
          <span ng-if="event.data && event.data.IpAddress">
            <span ng-if="event.data.IpAddress.length === 1">{{::event.data.IpAddress[0]}}</span>
            <abbr ng-if="event.data.IpAddress.length > 1" title="{{::event.data.IpAddress.join(',')}}">{{::event.data.IpAddress[0]}}</abbr>
          </span>
        </td>
        <td class="hidden-xs" ng-click="vm.open(event.id, $event)">
          <abbr ng-if="event.data.Name && event.data.Identity" title="{{::event.data.Name}} ({{::event.data.Identity}})" truncate overwrite-tooltip="false" ng-bind-template="{{::event.data.Name}}"></abbr>
          <span ng-if="!event.data.Name || !event.data.Identity" truncate ng-bind-template="{{::(event.data.Name || event.data.Identity)}}"></span>
        </td>
        <td ng-click="vm.open(event.id, $event)">
          <span ng-if="vm.hideSessionStartTime && event.data.Type === 'session'">--</span>
          <span ng-if="!vm.hideSessionStartTime || event.data.Type !== 'session'">
            <abbr title="{{::'DateTime' | translate:event}}">
                <span ng-if="vm.relativeTo()">{{::vm.afterRelativeText(event)}}<relative-time to="vm.relativeTo()" date="event.date"></relative-time>{{::vm.beforeRelativeText(event)}}</span>
                <span ng-if="!vm.relativeTo()"><timeago date="event.date"></timeago></span>
            </abbr>
          </span>
        </td>
      </tr>
      <tr ng-if="vm.events.length === 0 || vm.loading">
        <td class="hidden-xs" ng-if="vm.actions.length > 0"><label class="checks m-b-none"><input type="checkbox" disabled><i></i></label></td>
        <td colspan="3">
          <strong ng-if="vm.loading">{{::'Loading...' | translate}}</strong>
          <strong ng-if="!vm.loading">{{vm.hasFilter() ? 'No events were found with the current filter.': 'No events were found.' | translate}}</strong>
        </td>
      </tr>
    </tbody>
  </table>

  <div class="table-footer">
    <div class="row">
      <div class="col-sm-4 hidden-xs">
        <div class="dropdown" ng-if="vm.actions.length > 0">
          <button type="button" role="button" id="bulkActions" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
            {{::'Bulk Action' | translate}}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu" aria-labelledby="bulkActions">
            <li role="presentation"><a role="menuitem" tabindex="-1" ng-repeat="action in vm.actions" ng-click="vm.save(action)">{{::action.name | translate}}</a></li>
          </ul>
        </div>
      </div>
      <div class="col-sm-4 text-center" ng-class="vm.previous || vm.next ? 'col-xs-8': 'col-xs-12'" ng-if="vm.pageSummary">
        <small class="text-muted inline m-t-xs">{{vm.pageSummary}}</small>
      </div>
      <div class="col-sm-4 col-xs-4 text-right" ng-if="vm.previous || vm.next">
        <ul class="pagination pagination-sm m-t-none m-b-none">
          <li ng-show="vm.currentOptions.page && vm.currentOptions.page > 2"><a ng-click="vm.get()"><i class="fa fa-fast-backward"></i></a></li>
          <li ng-class="{'disabled': !vm.previous}"><a ng-disabled="!vm.previous" ng-click="!vm.previous || vm.previousPage()"><i class="fa fa-chevron-left"></i></a></li>
          <li ng-class="{'disabled': !vm.next}"><a ng-disabled="!vm.next" ng-click="!vm.next || vm.nextPage()"><i class="fa fa-chevron-right"></i></a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
