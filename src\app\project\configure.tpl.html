<organization-notifications organization-id="vm.project.organization_id" ignore-free="true" ignore-configure-projects="true"></organization-notifications>

<div class="hbox hbox-auto-xs hbox-auto-sm" refresh-on="PersistentEventChanged" refresh-if="vm.canRedirect(data)" refresh-action="vm.navigateToDashboard()">
  <div class="col">
    <div class="wrapper-md">
      <div class="panel panel-default">
        <div class="panel-heading"><i class="fa fa-cloud-download"></i><span ng-bind="'Download_Configure_Project' | translate:{projectName: vm.projectName}"></span></div>
        <div class="panel-body m-b-n">
          <p>{{::'The Exceptionless client can be integrated into your project in just a few easy steps.' | translate}}</p>
          <ol>
            <li>
              <p>{{::'Select your project type:' | translate}}</p>
              <select class="form-control" ng-model="vm.currentProjectType" ng-options="projectType.name group by projectType.platform for projectType in vm.projectTypes">
                <option value="" ng-selected="true" ng-if="!vm.currentProjectType.key">{{::'Please select a project type' | translate}}</option>
              </select>
            </li>
            <li ng-if="vm.isCommandLine()" style="margin-top: 15px;">
              <p>{{::'Execute the following in your shell:' | translate}}</p>
              <pre ng-if="vm.isBashShell()">curl "{{::vm.serverUrl}}/api/v2/events" \
    --request POST \
    --header "Authorization: Bearer {{::vm.apiKey}}" \
    --header "Content-Type: application/json" \
    --data-binary "[{'type':'log','message':'Hello World!'}]"</pre>
              <pre ng-if="!vm.isBashShell()">$body = @{
 "type"="log"
 "message"="Hello World!"
} | ConvertTo-Json

$header = @{
 "Authorization"="Bearer {{::vm.apiKey}}"
 "Content-Type"="application/json"
}

Invoke-RestMethod -Uri "{{::vm.serverUrl}}/api/v2/events" -Method "Post" -Body $body -Headers $header</pre>
            </li>
            <li style="margin-top: 15px;" ng-if="vm.currentProjectType.key && (vm.isDotNet() || vm.isJavaScript())">
              <div ng-if="vm.isDotNet()">
                <p ng-bind-html="'Notice_Install_Nuget_Package' | translate"></p>
                <pre><a ng-href="http://nuget.org/packages/{{vm.currentProjectType.key}}" target="_blank">Install-Package {{vm.currentProjectType.key}}</a></pre>
              </div>
              <div ng-if="vm.isJavaScript()">
                <div ng-if="!vm.isNode()">
                  <p>
                    <span ng-bind-html="'Notice_Install_Node_Package' | translate"></span>
                    <pre><a href="https://www.npmjs.com/package/@exceptionless/browser" target="_blank">npm install @exceptionless/browser --save</a></pre>
                    <span ng-bind-html="'Notice_Install_Manually' | translate"></span>
                  </p>
                </div>
                <div ng-if="vm.isNode()">
                  <p><span ng-bind-html="'Notice_Install_Node_Package' | translate"></span>
                  <pre><a href="https://www.npmjs.com/package/@exceptionless/node" target="_blank">npm install @exceptionless/node --save</a></pre></p>
                </div>
              </div>
            </li>
            <li ng-if="vm.isJavaScript()" style="margin-top: 15px;">
              <div ng-if="!vm.isNode()">
                <p>{{::'Configure the ExceptionlessClient with your Exceptionless API key:' | translate}}
                <pre>import { Exceptionless } from "@exceptionless/browser";<br /><br />await Exceptionless.startup(c => {<br />&nbsp;&nbsp;c.apiKey = "{{::vm.apiKey}}";<br />});</pre></p>
              </div>
              <div ng-if="vm.isNode()">
                <p>{{::'Configure the ExceptionlessClient with your Exceptionless API key:' | translate}}
                <pre>import { Exceptionless } from "@exceptionless/node";<br /><br />await Exceptionless.startup(c => {<br />&nbsp;&nbsp;c.apiKey = "{{::vm.apiKey}}";<br />});</pre></p>
              </div>
            </li>
            <li ng-if="vm.isDotNet()" style="margin-top: 15px;" >
              <div ng-if="vm.currentProjectType.key !== 'Exceptionless' && vm.currentProjectType.key !== 'Exceptionless.AspNetCore'">
                <p ng-bind-html="'Notice_Update_ApiKey' | translate:{config: vm.currentProjectType.config}"></p>

                <div class="input-group">
                  <input class="form-control api-key" type="text" ng-model="vm.apiKey" autofocus />
                  <a class="input-group-addon" title="{{::'Copy to Clipboard' | translate}}" clipboard text="vm.apiKey" on-copied="vm.copied()" on-error="vm.onCopyError()"><i class="fa fa-copy"></i></a>
                </div>
              </div>

              <div ng-if="vm.currentProjectType.key === 'Exceptionless'">
                <p ng-bind-html="'Notice_Application_Startup_NetFx' | translate:{apiKey: vm.apiKey}"></p>
                <p ng-bind-html="'Notice_Unhandled_Exception' | translate"></p>
              </div>
              <div ng-if="vm.currentProjectType.key === 'Exceptionless.AspNetCore'">
                  <p ng-bind-html="'Notice_Application_Startup_NetCore' | translate:{apiKey: vm.apiKey}"></p>
              </div>
            </li>
            <li style="margin-top: 15px;" ng-if="vm.currentProjectType.key === 'Exceptionless.Nancy'">
              <p ng-bind-html="'Notice_Application_Startup_Nancy' | translate"></p>
            </li>
            <li style="margin-top: 15px;" ng-if="vm.currentProjectType.key === 'Exceptionless.Windows' || vm.currentProjectType.key === 'Exceptionless.Wpf'">
              <p ng-bind-html="'Notice_Application_Startup_Windows' | translate"></p>
            </li>
            <li style="margin-top: 15px;" ng-if="vm.currentProjectType.key === 'Exceptionless.WebApi'">
              <p ng-bind-html="'Notice_Application_Startup_WebApi' | translate"></p>
              <p ng-bind-html="'Notice_Hosting_WebApi_In_AspNet' | translate"></p>
            </li>
          </ol>
          <p ng-if="vm.currentProjectType.key"><strong>{{::"That's it!" | translate}}</strong>
            <span ng-if="vm.isDotNet() || vm.isJavaScript()">{{::'Your project should now automatically be sending all unhandled exceptions to Exceptionless!' | translate}}</span>
            <span ng-if="vm.isDotNet()" ng-bind-html="'Notice_Submit_Exception' | translate:{docUrl: 'https://exceptionless.com/docs/clients/dotnet/sending-events/', sampleCode: 'ex.ToExceptionless().Submit()'}"></span>
            <span ng-if="vm.isJavaScript()" ng-bind-html="'Notice_Submit_Exception' | translate:{docUrl: 'https://exceptionless.com/docs/clients/javascript/sending-events/', sampleCode: 'await Exceptionless.submitException(ex);'}"></span>
          </p>

          <div class="alert alert-success" ng-if="vm.currentProjectType.key">
            <span ng-if="vm.isCommandLine()" ng-bind-html="'Notice_CommandLine_Application' | translate"></span>
            <span ng-if="vm.isDotNet()" ng-bind-html="'Notice_DotNet_Application' | translate"></span>
            <span ng-if="vm.isJavaScript()" ng-bind-html="'Notice_JavaScript_Application' | translate"></span>
          </div>
        </div>
        <footer class="panel-footer">
          <div class="pull-right">
            <a ui-sref="app.project-frequent({ projectId: vm.project.id })" class="btn btn-default" role="button">{{::'Go To Most Frequent' | translate}}</a>
          </div>
          <a ui-sref="app.account.manage({ tab: 'notifications', projectId: vm.project.id })" class="btn btn-primary" role="button" title="{{::'Manage Notification Settings' | translate}}">
            <i class="fa fa-fw fa-envelope"></i> <span class="hidden-xs">{{::'Manage Notification Settings' | translate}}</span>
          </a>
          <a ui-sref="app.project.manage({ id: vm.project.id })" class="btn btn-primary" role="button" title="{{::'Manage Project' | translate}}">
            <i class="fa fa-fw fa-gear"></i> <span class="hidden-xs">{{::'Manage Project' | translate}}</span>
          </a>
        </footer>
      </div>
    </div>
  </div>
</div>
