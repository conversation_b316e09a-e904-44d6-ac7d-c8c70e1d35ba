<div>
  <span ng-if="showBadge" class="label label-default {{badgeClass}}">{{::source.status | translate}}</span>

  <strong>
    <abbr title="{{::source.data.TypeFullName}}">{{::source.data.Type}}</abbr>
    <span ng-if="!source.data.Method">:</span>
  </strong>

    <span ng-if="source.data.Method"> in
        <strong>
          <abbr title="{{::source.data.MethodFullName}}">{{::source.data.Method}}</abbr>
        </strong>
    </span>

  <a ui-sref="app.stack({ id: source.id })" truncate lines="2">{{::source.title}}</a>
</div>

<div class="hidden-xs error-path" ng-if="source.data.Path"><i class="fa fa-caret-right"></i> <span truncate>{{::source.data.Path}}</span>
</div>
